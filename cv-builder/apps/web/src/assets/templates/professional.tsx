import { Document, Page, View, Text, Font } from '@react-pdf/renderer';
import { format } from 'date-fns';
import React from 'react';
import Html from 'react-pdf-html';
import {
  Section,
  WorkHistoryDataItem,
  AboutMe,
  Certifications,
  CustomSection,
  Education,
  Languages,
  PersonalInfo,
  Skills,
  WorkHistory,
  EducationDataItem,
} from 'shared/types';

import { mail, pin, phone } from './icons';
import { getSocialIcon } from './socialIcons';

interface ProfessionalTemplateProps {
  sections: Section[];
}

export function ProfessionalTemplate({ sections }: ProfessionalTemplateProps) {
  Font.register({
    family: 'Lato',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6uyw4BMUTPHvxk6XweuBCY.ttf',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u9w4BMUTPHh6UVew-FGC_p9dw.ttf',
        fontWeight: 'bold',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u8w4BMUTPHjxswWyWrFCbw7A.ttf',
        fontStyle: 'italic',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u_w4BMUTPHjxsI5wqPHA3s5dwt7w.ttf',
        fontWeight: 'bold',
        fontStyle: 'italic',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u9w4BMUTPHh50Xew-FGC_p9dw.ttf',
        fontWeight: 'heavy',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u_w4BMUTPHjxsI3wiPHA3s5dwt7w.ttf',
        fontWeight: 'heavy',
        fontStyle: 'italic',
      },
    ],
  });

  const styles = {
    // Header styles
    name: {
      fontFamily: 'Lato',
      fontWeight: 700,
      fontSize: 18,
      lineHeight: 1.35,
      color: '#000000',
    },
    contactText: {
      fontFamily: 'Lato',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.6,
      color: '#333333',
    },
    // Section styles
    sectionHeading: {
      fontFamily: 'Lato',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.6,
      color: '#527AAA',
      textTransform: 'uppercase' as const,
    },
    workTitle: {
      fontFamily: 'Lato',
      fontWeight: 900,
      fontSize: 10,
      lineHeight: 1.3,
      color: '#527AAA',
    },
    companyName: {
      fontFamily: 'Lato',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.3,
      color: '#333333',
    },
    dateText: {
      fontFamily: 'Lato',
      fontWeight: 900,
      fontSize: 10,
      lineHeight: 1.3,
      color: '#333333',
    },
    bodyText: {
      fontFamily: 'Lato',
      fontWeight: 600,
      fontSize: 10,
      lineHeight: 1.3,
      color: '#333333',
    },
    socialText: {
      fontFamily: 'Lato',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.6,
      color: '#666666',
    },
  };

  // Contact info component with icons
  const ContactInfo = ({ personalInfo }: { personalInfo: PersonalInfo }) => (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        gap: 14,
        marginBottom: 16,
      }}
    >
      <View style={{ flexDirection: 'column', gap: 4 }}>
        <Text style={styles.name}>
          {personalInfo.data.firstName.active &&
            personalInfo.data.firstName.value + ' '}
          {personalInfo.data.lastName.active &&
            personalInfo.data.lastName.value}
        </Text>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            rowGap: 4,
            columnGap: 8,
            flexWrap: 'wrap',
            alignItems: 'center',
            width: '80%',
          }}
        >
          {personalInfo.data.nationality.value &&
            personalInfo.data.nationality.active && (
              <Text style={styles.contactText}>
                Nationality: {personalInfo.data.nationality.value}
              </Text>
            )}
          {personalInfo.data.telephone.active &&
            personalInfo.data.telephone.value && (
              <View
                style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}
              >
                {phone}
                <Text style={styles.contactText}>
                  {personalInfo.data.telephone.value}
                </Text>
              </View>
            )}
          {personalInfo.data.email.active && personalInfo.data.email.value && (
            <View
              style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}
            >
              {mail}
              <Text style={styles.contactText}>
                {personalInfo.data.email.value}
              </Text>
            </View>
          )}
          {personalInfo.data.location.active &&
            personalInfo.data.location.value && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  columnGap: 4,
                  flexWrap: 'nowrap',
                }}
              >
                {pin}
                <Text style={{ fontWeight: 'bold' }}>
                  {personalInfo.data.location.value}
                </Text>
              </View>
            )}
        </View>
      </View>
    </View>
  );

  // Section container with background
  const SectionContainer = ({
    children,
    backgroundColor = '#FFFFFF',
    wrap = true,
  }: {
    children: React.ReactNode;
    backgroundColor?: string;
    wrap?: boolean;
  }) => (
    <View
      wrap={wrap}
      style={{
        backgroundColor,
        borderRadius: 4,
        padding: 8,
        marginBottom: 0,
      }}
    >
      {children}
    </View>
  );

  // Section heading with underline
  const SectionHeading = ({ title }: { title: string }) => (
    <View style={{ marginBottom: 4 }}>
      <Text style={styles.sectionHeading}>{title}</Text>
      <View
        style={{
          height: 1,
          backgroundColor: '#E0E0E0',
          width: '100%',
          marginTop: 4,
          marginBottom: 8,
        }}
      />
    </View>
  );

  // About me section
  const aboutMeBlock = (section: AboutMe) =>
    section.active && (
      <SectionContainer backgroundColor="#F2F2F2">
        <View style={{ marginBottom: 8 }}>
          <Text style={styles.sectionHeading}>{section.title}</Text>
        </View>
        <Html
          style={{
            fontFamily: 'Lato',
            fontSize: 10,
            lineHeight: 1.3,
            color: '#333333',
          }}
          renderers={{
            p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
          }}
        >
          {`${section.data.description}`}
        </Html>
      </SectionContainer>
    );

  // Work history item
  const workHistoryItem = (workHistRec: WorkHistoryDataItem) =>
    workHistRec.active && (
      <View style={{ marginBottom: 16 }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            width: '100%',
          }}
        >
          <Text style={styles.workTitle}>{workHistRec.roleTitle}</Text>
          <Text style={styles.dateText}>
            {workHistRec.startDate && format(workHistRec.startDate, 'yyyy')}
            {' - '}
            {workHistRec.isCurrent
              ? 'Present'
              : workHistRec.endDate && format(workHistRec.endDate, 'yyyy')}
          </Text>
        </View>
        <Text style={styles.companyName}>{workHistRec.companyName}</Text>
        <View style={{ marginTop: 8 }}>
          <Html
            style={{
              fontFamily: 'Lato',
              fontSize: 10,
              lineHeight: 1.3,
              color: '#333333',
            }}
            renderers={{
              p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
            }}
          >
            {`${workHistRec.description}`}
          </Html>
        </View>
      </View>
    );

  // Work history section
  const workHistoryBlock = (section: WorkHistory) =>
    section.active && (
      <SectionContainer>
        <SectionHeading title={section.title} />
        <View style={{ gap: 16 }}>
          {section.data
            .filter((h) => h.active)
            .map((histRec, i) => (
              <React.Fragment key={i}>
                {workHistoryItem(histRec)}
              </React.Fragment>
            ))}
        </View>
      </SectionContainer>
    );

  // Education item
  const educationItem = (education: EducationDataItem) =>
    education.active && (
      <SectionContainer backgroundColor="#F2F2F2">
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: 8,
          }}
        >
          <View style={{ flex: 1, paddingRight: 8 }}>
            <Text style={styles.workTitle}>{education.degree}</Text>
          </View>
          <Text style={styles.dateText}>
            {education.startDate && format(education.startDate, 'yyyy')}
            {' - '}
            {education.endDate && format(education.endDate, 'yyyy')}
          </Text>
        </View>
        <Text style={styles.companyName}>{education.schoolName}</Text>
      </SectionContainer>
    );

  // Education section
  const educationBlock = (section: Education) =>
    section.active && (
      <SectionContainer wrap={false}>
        <SectionHeading title={section.title} />
        <View style={{ flexDirection: 'row', gap: 16, flexWrap: 'wrap' }}>
          {section.data
            .filter((h) => h.active)
            .map((edRec, i) => (
              <View key={i} style={{ flexGrow: 1, flexBasis: '45%' }}>
                {educationItem(edRec)}
              </View>
            ))}
        </View>
      </SectionContainer>
    );

  // Skills section
  const skillsBlock = (section: Skills) =>
    section.active && (
      <SectionContainer>
        <SectionHeading title={section.title} />
        <View style={{ paddingLeft: 8 }}>
          <Html
            style={{
              fontFamily: 'Lato',
              fontSize: 10,
              lineHeight: 1.3,
              color: '#333333',
            }}
            renderers={{
              p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
            }}
          >
            {`${section.data.description}`}
          </Html>
        </View>
      </SectionContainer>
    );

  // Certifications section
  const certificationsBlock = (section: Certifications) =>
    section.active && (
      <SectionContainer>
        <SectionHeading title={section.title} />
        <View style={{ paddingLeft: 8 }}>
          <Html
            style={{
              fontFamily: 'Lato',
              fontSize: 10,
              lineHeight: 1.3,
              color: '#333333',
            }}
            renderers={{
              p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
            }}
          >
            {`${section.data.description}`}
          </Html>
        </View>
      </SectionContainer>
    );

  // Languages section
  const languagesBlock = (section: Languages) =>
    section.active && (
      <SectionContainer>
        <SectionHeading title={section.title} />
        <Text style={styles.companyName}>
          {section.data.languages.join(', ')}
        </Text>
      </SectionContainer>
    );

  // Social links section
  const socialLinksBlock = (personalInfo: PersonalInfo) => (
    <SectionContainer>
      <View style={{ flexDirection: 'row', gap: 8, flexWrap: 'wrap' }}>
        {personalInfo.data.socials.active &&
          personalInfo.data.socials.inputs.map((url, i) => (
            <View
              key={i}
              style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}
            >
              {getSocialIcon(url)}
              <Text style={styles.socialText}>
                {url.replace(/^https?:\/\//, '').replace(/^www\./, '')}
              </Text>
            </View>
          ))}
      </View>
    </SectionContainer>
  );

  // Custom section
  const customSection = (section: CustomSection) =>
    section.active && (
      <SectionContainer>
        <SectionHeading title={section.title} />
        <Html
          style={{
            fontFamily: 'Lato',
            fontSize: 10,
            lineHeight: 1.3,
            color: '#333333',
          }}
          renderers={{
            p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
          }}
        >
          {`${section.data.description}`}
        </Html>
      </SectionContainer>
    );

  const getSection = (section: Section) => {
    switch (section.id) {
      case 'personalInfo':
        return null; // Handled in header
      case 'aboutMe':
        return aboutMeBlock(section as AboutMe);
      case 'workHistory':
        return workHistoryBlock(section as WorkHistory);
      case 'education':
        return educationBlock(section as Education);
      case 'certifications':
        return certificationsBlock(section as Certifications);
      case 'skills':
        return skillsBlock(section as Skills);
      case 'languages':
        return languagesBlock(section as Languages);
      default:
        return customSection(section as CustomSection);
    }
  };

  const personalInfo = sections.find(
    (s) => s.id === 'personalInfo',
  ) as PersonalInfo;

  return (
    <Document>
      <Page
        style={{
          padding: 32,
          fontFamily: 'Lato',
          fontSize: 10,
          backgroundColor: '#FFFFFF',
        }}
        size="A4"
      >
        {/* Header with contact info */}
        {personalInfo && <ContactInfo personalInfo={personalInfo} />}

        {/* Main content sections */}
        <View style={{ gap: 0 }}>
          {sections
            .filter((s) => s.id !== 'personalInfo')
            .map((section, i) => (
              <React.Fragment key={i}>{getSection(section)}</React.Fragment>
            ))}

          {/* Social links at the bottom */}
          {personalInfo && socialLinksBlock(personalInfo)}
        </View>
      </Page>
    </Document>
  );
}
