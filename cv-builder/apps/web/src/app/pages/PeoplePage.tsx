import {
  keepPreviousData,
  useQuery,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { differenceInCalendarDays, format } from 'date-fns';
import {
  // AlignJustify,
  MoreHorizontal,
  // LayoutGrid,
} from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { SortOrder } from 'shared/dto/common.dto';
import { GetMembersSortBy } from 'shared/dto/members/get-members.dto';
import {
  Paging,
  currencyData,
  CvStatus,
  // GroupFilter,
  // groupFilterData,
  userTypeData,
  OrganizationFullInfo,
  Member,
} from 'shared/types';
import { toast } from 'sonner';

import { getMembersRequest, deleteMemberRequest } from '../helpers/requests';

import type { SortOption } from '@/components';

import {
  ButtonDanger,
  ButtonPrimary,
  ButtonSecondary,
  CustomerBadge,
  CvCountButton,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Drawer,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ImportForm,
  Loader,
  MemberAvatarWithSource,
  MemberEditForm,
  Pagination,
  SortDropdown,
} from '@/components';
import { Search } from '@/components/common';
import { LayoutContext } from '@/components/Layout/Layout';
import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { cn } from '@/lib/utils';

// enum SortOrder {
//   name = 'name',
//   cost = 'cost',
//   freshness = 'freshness',
// }

// enum View {
//   list = 'list',
//   grid = 'grid',
// }

const initialPaging = { page: 1, itemsPerPage: 12, total: 0 };

// CV freshness thresholds (in days)
const CV_FRESHNESS_YELLOW_THRESHOLD = 180; // 6 months
const CV_FRESHNESS_DRY_THRESHOLD = 365; // 1 year

type PeopleSortOption = SortOption & {
  sortBy: GetMembersSortBy;
  sortOrder: SortOrder;
};

const sortOptions: PeopleSortOption[] = [
  {
    label: 'Name (A-Z)',
    value: 'name_asc',
    sortBy: GetMembersSortBy.NAME,
    sortOrder: SortOrder.ASC,
  },
  {
    label: 'Name (Z-A)',
    value: 'name_desc',
    sortBy: GetMembersSortBy.NAME,
    sortOrder: SortOrder.DESC,
  },
  {
    label: 'Created (newest)',
    value: 'created_desc',
    sortBy: GetMembersSortBy.CREATED_AT,
    sortOrder: SortOrder.DESC,
  },
  {
    label: 'Created (oldest)',
    value: 'created_asc',
    sortBy: GetMembersSortBy.CREATED_AT,
    sortOrder: SortOrder.ASC,
  },
  {
    label: 'Updated (newest)',
    value: 'updated_desc',
    sortBy: GetMembersSortBy.UPDATED_AT,
    sortOrder: SortOrder.DESC,
  },
  {
    label: 'Updated (oldest)',
    value: 'updated_asc',
    sortBy: GetMembersSortBy.UPDATED_AT,
    sortOrder: SortOrder.ASC,
  },
];

export function PeoplePage() {
  const navigate = useNavigate();

  // const [sortOrder, setSortOrder] = useState<SortOrder>(SortOrder.name);
  // const [view, setView] = useState<View>(View.list);
  // const [groupFilter, setGroupFilter] = useState<GroupFilter>(GroupFilter.all);
  const [paging, setPaging] = useState<Paging>(initialPaging);
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedSort, setSelectedSort] = useState<PeopleSortOption>(
    sortOptions[0],
  );
  const [importDrawerActive, setImportDrawerActive] = useState<boolean>(false);
  const [editDrawerActive, setEditDrawerActive] = useState<boolean>(false);
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);

  const { setHeaderCallback } = useContext(LayoutContext);

  const { organization } = useAuth();
  const queryClient = useQueryClient();

  const { data: paginatedMembersData, isLoading } = useQuery({
    queryKey: [
      'paginatedMembers',
      {
        orgId: 'myOrg',
        page: paging.page,
        itemsPerPage: paging.itemsPerPage,
        search: searchValue,
        sortBy: selectedSort.sortBy,
        sortOrder: selectedSort.sortOrder,
      },
    ],
    queryFn: async () =>
      getMembersRequest(
        paging,
        searchValue,
        selectedSort.sortBy,
        selectedSort.sortOrder,
      ),
    placeholderData: keepPreviousData,
  });

  const deleteMemberMutation = useMutation({
    mutationFn: deleteMemberRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            totalMembers: Math.max(0, oldData.totalMembers - 1),
          };
        },
      );
      toast.success('Profile removed successfully');
      setDeleteDialogOpen(false);
      setMemberToDelete(null);
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      toast.error(message || 'Failed to remove profile');
    },
  });

  useEffect(() => {
    setHeaderCallback(
      isLoading
        ? 'Profiles Loading...'
        : `Profiles ${paginatedMembersData?.totalMembers}`,
    );

    return () => setHeaderCallback('');
  }, [isLoading, paginatedMembersData?.totalMembers, setHeaderCallback]);

  // Reset pagination when search changes
  useEffect(() => {
    setPaging((prev) => ({ ...prev, page: 1 }));
  }, [searchValue]);

  const handleSortChange = (option: PeopleSortOption) => {
    setSelectedSort(option);
    setPaging((prev) => ({ ...prev, page: 1 }));
  };

  // const viewDropdown = useMemo(() => {
  //   const options = [
  //     {
  //       name: 'List',
  //       icon: <AlignJustify size={16} />,
  //       active: view === View.list,
  //       onClick: () => setView(View.list),
  //     },
  //     {
  //       name: 'Grid',
  //       icon: <LayoutGrid size={16} />,
  //       active: view === View.grid,
  //       onClick: () => setView(View.grid),
  //     },
  //   ];

  //   return (
  //     <DropdownMenu modal={false}>
  //       <DropdownMenuTrigger asChild>
  //         <div className="flex space-x-1 cursor-pointer">
  //           {options.find((o) => o.active)?.icon}
  //           <ChevronDown size={16} />
  //         </div>
  //       </DropdownMenuTrigger>
  //       <DropdownMenuContent collisionPadding={10} align="end" className="w-20">
  //         <DropdownMenuGroup>
  //           {options.map((option) => (
  //             <DropdownMenuItem key={option.name} onClick={option.onClick}>
  //               <span className="text-smalldoge-3">{option.name}</span>
  //             </DropdownMenuItem>
  //           ))}
  //         </DropdownMenuGroup>
  //       </DropdownMenuContent>
  //     </DropdownMenu>
  //   );
  // }, [view]);

  // const groupFilterDropdown = useMemo(
  //   () => (
  //     <div className="flex space-x-1">
  //       <DropdownMenu modal={false}>
  //         <DropdownMenuTrigger asChild>
  //           <div className="flex items-center bg-msWhite border border-msGray-5 rounded-[100px] px-2 cursor-pointer">
  //             <span className="font-bold text-smalldoge-4">
  //               Showing {groupFilterData[groupFilter].name}
  //             </span>
  //             <ChevronDown size={16} />
  //           </div>
  //         </DropdownMenuTrigger>
  //         <DropdownMenuContent
  //           collisionPadding={10}
  //           align="end"
  //           className="w-20"
  //         >
  //           <DropdownMenuGroup>
  //             {Object.values(GroupFilter).map((group) => (
  //               <DropdownMenuItem
  //                 key={group}
  //                 onClick={() => setGroupFilter(group)}
  //               >
  //                 <span className="text-smalldoge-3">
  //                   {groupFilterData[group].name}
  //                 </span>
  //               </DropdownMenuItem>
  //             ))}
  //           </DropdownMenuGroup>
  //         </DropdownMenuContent>
  //       </DropdownMenu>
  //     </div>
  //   ),
  //   [groupFilter],
  // );

  const profileToEdit = useMemo(() => {
    return paginatedMembersData?.members.find(
      (member) => member._id === selectedProfile,
    );
  }, [paginatedMembersData, selectedProfile]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader />
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col h-full px-4 py-2">
        <div className="flex w-full mb-4">
          <span className="hidden font-black text-bigdoge-6 md:inline-block">
            Profiles {paginatedMembersData?.totalMembers}
          </span>
          <div className="flex items-center ml-auto space-x-6">
            <SortDropdown
              sortOptions={sortOptions}
              selectedSort={selectedSort}
              onSortChange={handleSortChange as (option: SortOption) => void}
            />

            <Search
              value={searchValue}
              onChange={setSearchValue}
              placeholder="Search"
            />
            <ButtonPrimary
              variant="blackCompact"
              onClick={() => setImportDrawerActive(true)}
            >
              <span>New profile</span>
            </ButtonPrimary>
          </div>
        </div>
        <div className="flex justify-between mb-3">
          {/* {groupFilterDropdown} */}
          <div></div>
          <Pagination
            page={paging.page}
            itemsPerPage={paging.itemsPerPage}
            total={paginatedMembersData?.totalMembers}
            onItemsPerPageChange={(val) =>
              setPaging({ ...paging, itemsPerPage: val, page: 1 })
            }
            onPageChange={(page) => setPaging({ ...paging, page })}
          />
        </div>
        {/* TABLE */}
        <div className="w-full overflow-auto">
          <table className="table-fixed w-full min-w-[1100px] lg:min-w-full divide-y divide-msGray-5">
            <thead className="pb-1">
              <tr>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[11%]"
                >
                  Data from
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[16%]"
                >
                  Member
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[9%]"
                >
                  Cost rate
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[17%]"
                >
                  Contract renewal
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[15%]"
                >
                  Current assignment
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[14%]"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[10%]"
                >
                  CV freshness
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[8%]"
                >
                  CVs created
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-center w-[5%]"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-msGray-5">
              {paginatedMembersData?.members &&
              paginatedMembersData.members.length > 0 ? (
                paginatedMembersData.members.map((member) => {
                  const activeCVs = member.cvs.filter(
                    (cv) => cv.status === CvStatus.active,
                  );

                  return (
                    <tr
                      key={member._id}
                      className="transition-colors duration-100 cursor-pointer hover:bg-msGray-6"
                      onClick={() =>
                        navigate(`${NAVIGATE_PATH.cvList}/${member._id}`)
                      }
                    >
                      {/* Data from */}
                      <td className="py-2 pr-2">
                        <MemberAvatarWithSource member={member} />
                      </td>
                      {/* Member */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col">
                          <span className="font-bold truncate text-smalldoge-3">
                            {member.firstName || member.lastName
                              ? `${member.firstName || ''} ${
                                  member.lastName || ''
                                }`.trim()
                              : '-'}
                          </span>
                          <span className="truncate text-smalldoge-5 text-msGray-2">
                            {member.currentPosition || '-'}
                          </span>
                        </div>
                      </td>
                      {/* Cost rate */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col space-y-2">
                          {member.costRate ? (
                            <div className="flex items-center h-6">
                              <span className="text-smalldoge-5 text-msGray-2">
                                {currencyData[member.costRate.currency].sign}{' '}
                                {member.costRate.amount}
                              </span>
                            </div>
                          ) : (
                            <span className="text-smalldoge-5 text-msGray-2">
                              -
                            </span>
                          )}
                        </div>
                      </td>
                      {/* Contract renewal */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col space-y-2">
                          {activeCVs.length > 0 ? (
                            activeCVs.map((cv) => (
                              <div
                                key={cv._id}
                                className="flex items-center h-6"
                              >
                                <span
                                  className={cn(
                                    'text-smalldoge-5 text-msGray-2',
                                    !cv.preferences.autoRenewal &&
                                      'text-msPink-1',
                                  )}
                                >
                                  <b>
                                    {cv.preferences.autoRenewal
                                      ? 'Auto renewal'
                                      : 'Ends'}
                                  </b>{' '}
                                  {cv.preferences.contractEnd
                                    ? format(
                                        new Date(cv.preferences.contractEnd),
                                        'PP',
                                      )
                                    : '-'}
                                </span>
                              </div>
                            ))
                          ) : (
                            <span className="text-smalldoge-5 text-msGray-2">
                              -
                            </span>
                          )}
                        </div>
                      </td>
                      {/* Current assignment */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col space-y-2">
                          {activeCVs.length > 0 ? (
                            activeCVs.map((cv) => (
                              <div
                                key={cv._id}
                                className="flex items-center h-6"
                              >
                                <CustomerBadge
                                  name={cv.preferences.customer?.name}
                                />
                              </div>
                            ))
                          ) : (
                            <span className="text-smalldoge-5 text-msGray-2">
                              -
                            </span>
                          )}
                        </div>
                      </td>
                      {/* Type */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col space-y-2">
                          {member.type ? (
                            <div className="flex items-center h-6">
                              <div
                                className={cn(
                                  'w-fit max-w-full flex bg-msGray-6 rounded-[100px] px-2',
                                  userTypeData[member.type].color,
                                )}
                              >
                                <span className="truncate text-smalldoge-3">
                                  {userTypeData[member.type].name}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <span className="text-smalldoge-5 text-msGray-2">
                              -
                            </span>
                          )}
                        </div>
                      </td>
                      {/* CV freshness */}
                      <td className="py-2 pr-2">
                        <div className="flex flex-col space-y-2">
                          {activeCVs.length > 0 ? (
                            activeCVs.map((cv) => {
                              const daysFromCreation = differenceInCalendarDays(
                                new Date(),
                                new Date(cv.createdAt),
                              );

                              return (
                                <div
                                  key={cv._id}
                                  className="flex items-center h-6"
                                >
                                  <img
                                    className="mr-2"
                                    src={`/icons/${
                                      daysFromCreation <
                                      CV_FRESHNESS_YELLOW_THRESHOLD
                                        ? 'TreeGreen'
                                        : daysFromCreation <
                                            CV_FRESHNESS_DRY_THRESHOLD
                                          ? 'TreeYellow'
                                          : 'TreeDry'
                                    }.svg`}
                                    alt="tree"
                                  />
                                  <span className="mr-auto text-smalldoge-5 text-msGray-2">
                                    {format(new Date(cv.createdAt), 'PP')}
                                  </span>
                                </div>
                              );
                            })
                          ) : (
                            <span className="text-smalldoge-5 text-msGray-2">
                              -
                            </span>
                          )}
                        </div>
                      </td>
                      {/* CVs created */}
                      <td className="py-2 pr-2">
                        <CvCountButton
                          count={member.cvs.length}
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`${NAVIGATE_PATH.cvList}/${member._id}`);
                          }}
                        />
                      </td>
                      {/* Actions */}
                      <td className="py-2 pr-2">
                        <div className="flex justify-center">
                          <DropdownMenu modal={false}>
                            <DropdownMenuTrigger asChild>
                              <button
                                className="p-1 transition-colors rounded hover:bg-msGray-6"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal
                                  size={16}
                                  className="transition-colors duration-200 text-msGray-3 hover:text-msGray-1"
                                />
                              </button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              collisionPadding={10}
                              align="end"
                              className="w-38"
                            >
                              <DropdownMenuGroup>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(
                                      `${NAVIGATE_PATH.cvList}/${member._id}`,
                                    );
                                  }}
                                >
                                  <span className="text-smalldoge-3">
                                    View CVs
                                  </span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditDrawerActive(true);
                                    setSelectedProfile(member._id);
                                  }}
                                >
                                  <span className="text-smalldoge-3">
                                    Edit base profile
                                  </span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setMemberToDelete(member);
                                    setDeleteDialogOpen(true);
                                  }}
                                >
                                  <span className="text-smalldoge-3 text-msRed-1">
                                    Remove Profile
                                  </span>
                                </DropdownMenuItem>
                              </DropdownMenuGroup>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={9} className="p-6 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <img
                        src="/images/no-profiles-placeholder.svg"
                        alt="No profiles found"
                        className="mb-4"
                      />
                      <p className="text-msGray-3 text-smalldoge-3">
                        No profiles found
                        {searchValue && ' matching your search'}.
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      {/* Profile Edit */}
      <Drawer
        active={editDrawerActive}
        onClose={() => setEditDrawerActive(false)}
      >
        {profileToEdit && organization && (
          <MemberEditForm
            orgId={organization._id}
            member={profileToEdit}
            onClose={() => setEditDrawerActive(false)}
          />
        )}
      </Drawer>
      {/* Import member */}
      <Drawer
        active={importDrawerActive}
        onClose={() => setImportDrawerActive(false)}
      >
        {organization && (
          <ImportForm
            organization={organization}
            onClose={() => setImportDrawerActive(false)}
          />
        )}
      </Drawer>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Profile</DialogTitle>
            <DialogDescription>
              {memberToDelete && (
                <>
                  Are you sure you want to remove{' '}
                  <strong
                    className="max-w-[400px] truncate inline-block align-bottom"
                    title={`${memberToDelete.firstName} ${memberToDelete.lastName}`}
                  >
                    {memberToDelete.firstName} {memberToDelete.lastName}
                  </strong>
                  ? This will permanently delete{' '}
                  <strong>{memberToDelete.cvs?.length || 0}</strong> associated
                  CVs.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <ButtonSecondary
              onClick={() => {
                setDeleteDialogOpen(false);
                setMemberToDelete(null);
              }}
            >
              Cancel
            </ButtonSecondary>
            <ButtonDanger
              variant="pink"
              onClick={() => {
                if (memberToDelete) {
                  deleteMemberMutation.mutate(memberToDelete._id);
                }
              }}
              disabled={deleteMemberMutation.isPending}
            >
              {deleteMemberMutation.isPending
                ? 'Removing...'
                : 'Remove Profile'}
            </ButtonDanger>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
