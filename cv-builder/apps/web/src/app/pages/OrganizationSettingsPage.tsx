import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { formatDistanceToNow } from 'date-fns';
import { ChevronDown } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { DEFAULT_ORG_IMAGE } from 'shared/constants';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button, ButtonDanger, ButtonSecondary, Avatar } from '@/components';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/common/Dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/common/DropdownMenu';
import { ImageInput } from '@/components/common/ImageInput/ImageInput';
import { Input } from '@/components/common/Input';
import { Label } from '@/components/common/Label';
import { Loader } from '@/components/common/Loader';
import { Separator } from '@/components/common/Separator/Separator';
import { Textarea } from '@/components/common/Textarea';
import { TypographyPageTitle, TypographyMuted } from '@/components/Settings';
import { SettingsBadge } from '@/components/Settings/Badge/Badge';
import { TypographyLabel } from '@/components/Settings/Typography/Typography';
import { useAuth } from '@/contexts/AuthContext';
import {
  deleteOrganizationRequest,
  getOrganizationFullInfoRequest,
  updateOrganizationRequest,
  getCurrentUserRequest,
  cancelOrganizationDeletionRequest,
} from '@/helpers/requests';
import { getCommonTimezones } from '@/helpers/timezones';
import { cn } from '@/lib/utils';

// Form schema
const organizationSettingsSchema = z.object({
  name: z
    .string()
    .min(1, 'Organization name is required')
    .max(100, 'Organization name must be 100 characters or less'),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  website: z
    .string()
    .max(100, 'Website must be 100 characters or less')
    .optional(),
  timezone: z.string().optional(),
  locations: z
    .array(z.string())
    .max(5, 'Maximum 5 locations allowed')
    .optional(),
  photo: z.string().nullable().optional(),
  photoFile: z.instanceof(File).nullable().optional(),
});

type OrganizationSettingsFormData = z.infer<typeof organizationSettingsSchema>;

// Local Typography Components
function TypographySettingsLabel({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-smalldoge-3 font-bold text-msGray-3', className)}>
      {children}
    </p>
  );
}

function TypographySettingsValue({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-smalldoge-3 font-bold text-msGray-2', className)}>
      {children}
    </p>
  );
}

function TypographySettingsSectionTitle({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-smalldoge-2 font-bold text-msBlack', className)}>
      {children}
    </p>
  );
}

// Main Page
export function OrganizationSettingsPage() {
  const queryClient = useQueryClient();
  const { canDeleteOrganization, canUpdateOrganization, refreshUserData } =
    useAuth();
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletePassword, setDeletePassword] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // Get timezone options
  const timezoneOptions = getCommonTimezones();

  const { data: organizationData, isLoading } = useQuery({
    queryKey: ['organizationFullInfo'],
    queryFn: getOrganizationFullInfoRequest,
  });

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { isDirty, errors },
  } = useForm<OrganizationSettingsFormData>({
    resolver: zodResolver(organizationSettingsSchema),
    defaultValues: {
      name: '',
      description: '',
      website: '',
      timezone: '',
      locations: [],
      photo: null,
      photoFile: null,
    },
  });

  // Update form with organization data when it loads
  useEffect(() => {
    if (organizationData) {
      reset({
        name: organizationData.name || '',
        description: organizationData.description || '',
        website: organizationData.website || '',
        timezone: organizationData.timezone || '',
        locations: organizationData.locations || [],
      });
    }
  }, [organizationData, reset]);

  const { mutate: updateOrganization, isPending: isUpdatingOrganization } =
    useMutation({
      mutationFn: ({ dto, photoFile }: { dto: any; photoFile?: File | null }) =>
        updateOrganizationRequest(dto, photoFile),
      onSuccess: async () => {
        toast.success('Organization settings updated successfully');
        queryClient.invalidateQueries({ queryKey: ['organizationFullInfo'] });
        await refreshUserData();
        setIsEditing(false);
      },
      onError: (error: AxiosError) => {
        toast.error(
          (error?.response?.data as { message: string })?.message ||
            'Failed to update organization settings',
        );
      },
    });

  const { mutate: deleteOrganization, isPending: isDeletingOrganization } =
    useMutation({
      mutationFn: deleteOrganizationRequest,
      onSuccess: async () => {
        toast.success('Workspace scheduled for deletion successfully.');
        setDeletePassword('');
        setIsDeleteDialogOpen(false);
        queryClient.clear();
        // Refresh user data to trigger organization creation flow
        try {
          const userResponse = await getCurrentUserRequest();
          if (userResponse.needsOrganizationCreation) {
            window.location.href = '/create-organization';
          } else {
            window.location.reload();
          }
        } catch (error) {
          console.error('Failed to refresh user data:', error);
          window.location.reload();
        }
      },
      onError: (error: AxiosError) => {
        toast.error(
          (error?.response?.data as { message: string })?.message ||
            'Failed to schedule workspace deletion.',
        );
        setDeletePassword('');
      },
    });

  const cancelOrganizationDeletionMutation = useMutation({
    mutationFn: cancelOrganizationDeletionRequest,
    onSuccess: async () => {
      toast.success('Workspace deletion canceled successfully');
      // Refresh organization data to hide the deletion countdown
      queryClient.invalidateQueries({ queryKey: ['organizationFullInfo'] });
      await refreshUserData();
    },
    onError: (error: AxiosError) => {
      const message =
        (error?.response?.data as { message: string })?.message ||
        'Failed to cancel workspace deletion';
      toast.error(message);
    },
  });

  const handleDeleteOrganization = () => {
    if (!deletePassword.trim()) {
      toast.error('Password is required');
      return;
    }
    deleteOrganization(deletePassword);
  };

  useEffect(() => {
    if (organizationData?.photo) {
      setLogoUrl(organizationData.photo);
    } else {
      setLogoUrl(DEFAULT_ORG_IMAGE);
    }
  }, [organizationData]);

  const handleImageUpdate = (file: File | null, previewUrl: string | null) => {
    setLogoUrl(previewUrl);
    setValue('photoFile', file, { shouldDirty: true });
    setValue('photo', previewUrl, { shouldDirty: true });
  };

  const onSubmit = (data: OrganizationSettingsFormData) => {
    const dto: {
      name?: string;
      description?: string;
      website?: string;
      timezone?: string;
      locations?: string[];
      photo?: string | null;
    } = {
      name: data.name,
      description: data.description,
      website: data.website,
      timezone: data.timezone,
      locations: data.locations,
    };

    if (data.photoFile) {
      // Don't include photo URL in dto when we have a file
      dto.photo = undefined;
    } else {
      dto.photo = data.photo;
    }

    if (data.photo === null && !data.photoFile && organizationData?.photo) {
      dto.photo = null;
    }

    updateOrganization({ dto, photoFile: data.photoFile });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader />
      </div>
    );
  }

  if (!organizationData) {
    return (
      <div className="flex items-center justify-center h-full">
        <TypographyMuted>Could not load organization data.</TypographyMuted>
      </div>
    );
  }

  const {
    name,
    locations = [],
    description,
    website,
    timezone,
    totalMembers,
    totalCvs,
  } = organizationData;

  return (
    <div className="flex flex-col max-w-xl gap-6 px-4 py-10 mx-auto">
      <div className="flex items-center justify-between">
        <TypographyPageTitle>Team Settings</TypographyPageTitle>
        {canUpdateOrganization && !isEditing && (
          <ButtonSecondary onClick={() => setIsEditing(true)}>
            Edit Settings
          </ButtonSecondary>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* General Organization Info */}
        <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 gap-y-6">
          {/* Company Logo */}
          <div className="flex flex-col items-start self-start">
            <TypographySettingsLabel>Company Logo</TypographySettingsLabel>
            <TypographyMuted>Recommended size is 256x256px</TypographyMuted>
          </div>
          <div className="self-start">
            {isEditing ? (
              <ImageInput
                url={logoUrl}
                onImageUpdate={handleImageUpdate}
                disabled={!canUpdateOrganization}
                error={errors.photo?.message || errors.photoFile?.message}
              />
            ) : (
              <Avatar url={logoUrl} size={64} />
            )}
          </div>

          {/* Company Name */}
          <TypographySettingsLabel>Company name</TypographySettingsLabel>
          {canUpdateOrganization && isEditing ? (
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter company name"
                  className="max-w-sm"
                  error={errors.name?.message}
                />
              )}
            />
          ) : (
            <TypographySettingsValue>{name || '-'}</TypographySettingsValue>
          )}

          {/* Locations */}
          <TypographySettingsLabel className="self-start">
            Locations
          </TypographySettingsLabel>
          {canUpdateOrganization && isEditing ? (
            <Controller
              name="locations"
              control={control}
              render={({ field }) => (
                <div className="flex flex-col gap-2">
                  <Input
                    placeholder="Add locations (comma separated)"
                    className="max-w-sm"
                    onBlur={(e) => {
                      const locationsList = e.target.value
                        .split(',')
                        .map((loc) => loc.trim())
                        .filter(Boolean);
                      field.onChange(locationsList);
                    }}
                    defaultValue={field.value?.join(', ')}
                    error={errors.locations?.message}
                  />
                  <div className="flex flex-wrap gap-2 mt-2">
                    {field.value?.map((location: string) => (
                      <SettingsBadge key={location}>{location}</SettingsBadge>
                    ))}
                  </div>
                </div>
              )}
            />
          ) : (
            <div className="flex flex-wrap gap-2">
              {locations?.length > 0 ? (
                locations.map((location: string) => (
                  <SettingsBadge key={location}>{location}</SettingsBadge>
                ))
              ) : (
                <TypographyMuted>No locations specified.</TypographyMuted>
              )}
            </div>
          )}

          {/* Description */}
          <TypographySettingsLabel className="self-start">
            Description
          </TypographySettingsLabel>
          {canUpdateOrganization && isEditing ? (
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  placeholder="Enter organization description"
                  className="max-w-sm"
                  rows={4}
                  error={errors.description?.message}
                />
              )}
            />
          ) : (
            <p className="self-start text-smalldoge-4">
              {description || 'No description provided.'}
            </p>
          )}

          {/* Website */}
          <TypographySettingsLabel>Website</TypographySettingsLabel>
          {canUpdateOrganization && isEditing ? (
            <Controller
              name="website"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter website URL"
                  className="max-w-sm"
                  error={errors.website?.message}
                />
              )}
            />
          ) : (
            <TypographySettingsValue>{website || '-'}</TypographySettingsValue>
          )}
        </div>

        <Separator className="my-6" />

        {/* Members Section */}
        <div className="flex flex-col gap-3">
          <TypographySettingsSectionTitle>
            Members
          </TypographySettingsSectionTitle>
          <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 gap-y-4">
            <TypographySettingsLabel>Total CV profiles</TypographySettingsLabel>
            <TypographySettingsValue>
              {totalMembers ?? 0}
            </TypographySettingsValue>
            <TypographySettingsLabel>Total CVs created</TypographySettingsLabel>
            <TypographySettingsValue>{totalCvs ?? 0}</TypographySettingsValue>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Time Section */}
        <div className="flex flex-col gap-3">
          <TypographySettingsSectionTitle>Time</TypographySettingsSectionTitle>
          <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 gap-y-4">
            <TypographySettingsLabel>Timezone</TypographySettingsLabel>
            {canUpdateOrganization && isEditing ? (
              <Controller
                name="timezone"
                control={control}
                render={({ field }) => (
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <div className="flex items-center cursor-pointer bg-background text-foreground">
                        <TypographyLabel>
                          {field.value
                            ? timezoneOptions.find(
                                (tz) => tz.value === field.value,
                              )?.label || field.value
                            : 'Select timezone'}
                        </TypographyLabel>
                        <ChevronDown className="w-4 h-4 ml-1" />
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      collisionPadding={10}
                      align="start"
                      className="w-fit prevent-drawer-outside-click max-h-[300px] overflow-y-auto"
                    >
                      <DropdownMenuGroup>
                        {timezoneOptions.map((option) => (
                          <DropdownMenuItem
                            key={option.value}
                            onClick={() => field.onChange(option.value)}
                          >
                            <TypographyLabel>{option.label}</TypographyLabel>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              />
            ) : (
              <TypographySettingsValue>
                {timezone
                  ? timezoneOptions.find((tz) => tz.value === timezone)
                      ?.label || timezone
                  : 'Not set'}
              </TypographySettingsValue>
            )}
          </div>
        </div>

        {/* Form Actions */}
        {canUpdateOrganization && isEditing && (
          <div className="flex justify-end gap-2 mt-6">
            <ButtonSecondary
              type="button"
              onClick={() => {
                setIsEditing(false);
                reset();
              }}
              disabled={isUpdatingOrganization}
            >
              Cancel
            </ButtonSecondary>
            <ButtonSecondary
              type="submit"
              disabled={!isDirty || isUpdatingOrganization}
            >
              {isUpdatingOrganization ? 'Saving...' : 'Save Changes'}
            </ButtonSecondary>
          </div>
        )}
      </form>

      <Separator className="my-2" />

      {/* Organization Deletion Countdown - Show when deletion is scheduled */}
      {organizationData?.deletionScheduledDate && (
        <div className="flex flex-col gap-3 mt-20">
          <TypographySettingsSectionTitle>
            Workspace Status
          </TypographySettingsSectionTitle>
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-msRed-1 animate-pulse"></div>
              <TypographySettingsValue className="text-msRed-1">
                Workspace deletion scheduled
              </TypographySettingsValue>
            </div>
            <div className="p-3 border rounded-lg bg-msRed-3/10 border-msRed-2/20">
              <p className="mb-2 font-semibold text-smalldoge-3 text-msRed-1">
                This workspace will be permanently deleted{' '}
                {formatDistanceToNow(
                  new Date(organizationData.deletionScheduledDate),
                  {
                    addSuffix: true,
                  },
                )}
              </p>
              <p className="mb-2 text-smalldoge-5 text-msGray-3">
                Deletion date:{' '}
                {new Date(
                  organizationData.deletionScheduledDate,
                ).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </p>
              <p className="mb-3 text-smalldoge-5 text-msGray-3">
                After the deletion date, all workspace data will be permanently
                removed and cannot be recovered.
              </p>
              <ButtonSecondary
                onClick={() => cancelOrganizationDeletionMutation.mutate()}
                disabled={cancelOrganizationDeletionMutation.isPending}
                className="w-fit"
              >
                {cancelOrganizationDeletionMutation.isPending
                  ? 'Canceling...'
                  : 'Cancel Deletion'}
              </ButtonSecondary>
            </div>
          </div>
        </div>
      )}

      {/* Danger Zone Section - Only visible to owners */}
      {canDeleteOrganization && !organizationData?.deletionScheduledDate && (
        <div className="flex flex-col gap-3 mt-20">
          <TypographySettingsSectionTitle>
            Danger zone
          </TypographySettingsSectionTitle>
          <div className="p-4 rounded-lg bg-msGray-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <TypographySettingsValue>
                  Delete workspace
                </TypographySettingsValue>
                <TypographyMuted>
                  Schedule workspace to be permanently deleted
                </TypographyMuted>
              </div>
              <Dialog
                open={isDeleteDialogOpen}
                onOpenChange={(open) => {
                  setIsDeleteDialogOpen(open);
                  if (!open) {
                    setDeletePassword('');
                  }
                }}
              >
                <DialogTrigger asChild>
                  <ButtonDanger>Delete workspace</ButtonDanger>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Schedule Workspace Deletion?</DialogTitle>
                    <DialogDescription>
                      <p className="mb-2">
                        This workspace will be scheduled for deletion in 7 days.
                        During this period:
                      </p>
                      <ul className="pl-5 mb-2 list-disc">
                        <li>All users will be notified</li>
                        <li>
                          Users with other workspaces will be moved to their
                          next available workspace
                        </li>
                        <li>New invites will be blocked</li>
                      </ul>
                      <p className="font-semibold text-msRed-1">
                        After 7 days, all data will be permanently deleted and
                        cannot be recovered.
                      </p>
                    </DialogDescription>
                  </DialogHeader>
                  <div className="my-4">
                    <Label
                      label="Confirm your password"
                      labelProps={{ htmlFor: 'delete-password' }}
                    />
                    <Input
                      id="delete-password"
                      type="password"
                      value={deletePassword}
                      onChange={(e) => setDeletePassword(e.target.value)}
                      placeholder="Enter your password"
                      className="mt-2"
                    />
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <ButtonSecondary>Cancel</ButtonSecondary>
                    </DialogClose>
                    <ButtonDanger
                      variant="pink"
                      onClick={handleDeleteOrganization}
                      disabled={
                        isDeletingOrganization || !deletePassword.trim()
                      }
                    >
                      {isDeletingOrganization
                        ? 'Scheduling...'
                        : 'Schedule Deletion'}
                    </ButtonDanger>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
