import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { format } from 'date-fns';
import { MoreHorizontal } from 'lucide-react';
import { useState, PropsWithChildren } from 'react';
import { UserRole, InviteStatus, Invite } from 'shared/types';
import { toast } from 'sonner';

import { Separator } from '@/components';
import { Avatar } from '@/components/common/Avatar/Avatar';
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/common/DropdownMenu';
import { Loader } from '@/components/common/Loader';
import { Search } from '@/components/common/Search/Search';
import { EditUserDrawer } from '@/components/OrganizationUsers/EditUserDrawer';
import { InviteUserDrawer } from '@/components/OrganizationUsers/InviteUserDrawer';
import {
  getOrganizationUsersRequest,
  getOrganizationInvitesRequest,
  resendOrganizationInviteRequest,
  deleteOrganizationInviteRequest,
} from '@/helpers/requests';

// Keep User interface here as it's used for table data and selectedUser state
interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  joinedDate?: string;
  avatar?: string;
}

interface UserRoleBadgeProps {
  role: UserRole;
}

const UserRoleBadge: React.FC<UserRoleBadgeProps> = ({ role }) => (
  <span
    className={`px-3 py-1 rounded-full text-smalldoge-4 font-medium ${
      role === UserRole.OWNER
        ? 'bg-msBlue-4 text-msBlue-1'
        : role === UserRole.ADMIN
          ? 'bg-msGreen-4 text-msGreen-1'
          : 'bg-msGray-5 text-msGray-1'
    }`}
  >
    {role.charAt(0) + role.slice(1).toLowerCase()}
  </span>
);

// Typography Components
const ListHeader: React.FC<PropsWithChildren<{ className?: string }>> = ({
  children,
  className = '',
}) => (
  <th
    className={`pb-1 pt-2 text-left text-smalldoge-5 font-normal text-msGray-3 ${className}`}
  >
    {children}
  </th>
);

const UserSubtext: React.FC<PropsWithChildren> = ({ children }) => (
  <div className="font-normal text-smalldoge-5 text-msGray-2">{children}</div>
);

const formatJoinedDate = (dateString?: string) => {
  if (!dateString) {
    return '-';
  }
  const date = new Date(dateString);
  return format(date, 'MMM dd, yyyy');
};

export function OrganizationUsersPage() {
  const queryClient = useQueryClient();
  const [isInviteDrawerOpen, setIsInviteDrawerOpen] = useState(false);
  const [isUserDetailsDrawerOpen, setIsUserDetailsDrawerOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const { data: users, isLoading: isLoadingUsers } = useQuery({
    queryKey: ['organizationUsers', searchTerm],
    queryFn: () => getOrganizationUsersRequest(searchTerm),
  });

  const { data: invites, isLoading: isLoadingInvites } = useQuery({
    queryKey: ['organizationInvites'],
    queryFn: getOrganizationInvitesRequest,
  });

  const { mutate: resendInvite, isPending: isResendingInvite } = useMutation({
    mutationFn: (dto: { email: string; role: UserRole }) =>
      resendOrganizationInviteRequest(dto),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationInvites'] });
      toast.success('Invitation resent successfully');
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      toast.error(message || 'Failed to resend invitation');
    },
  });

  const { mutate: deleteInvite, isPending: isDeletingInvite } = useMutation({
    mutationFn: (inviteId: string) => deleteOrganizationInviteRequest(inviteId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationInvites'] });
      toast.success('Invitation cancelled successfully');
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      toast.error(message || 'Failed to cancel invitation');
    },
  });

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setIsUserDetailsDrawerOpen(true);
  };

  const handleResendInvite = (invite: Invite) => {
    resendInvite({ email: invite.email, role: invite.role });
  };

  const handleCancelInvite = (inviteId: string) => {
    deleteInvite(inviteId);
  };

  // Show all invites (not just pending)
  const allInvites = invites || [];

  return (
    <div className="bg-msGray-7">
      <div>
        <div className="flex items-center justify-end p-2">
          <Search
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search Members"
            bgColor="bg-msGray-6"
          />
          <ButtonPrimary
            variant="blackCompact"
            onClick={() => setIsInviteDrawerOpen(true)}
            className="px-2 py-1 ml-4"
          >
            Invite
          </ButtonPrimary>
        </div>

        <Separator />

        {/* Invitations */}
        <div className="mt-6">
          <h2 className="mb-4 px-4 text-lg font-semibold text-msBlack">
            Invitations
          </h2>

          {isLoadingInvites ? (
            <div className="flex items-center justify-center h-full">
              <Loader />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-msGray-5">
                    <ListHeader className="w-1/4 pl-4">Email</ListHeader>
                    <ListHeader className="w-1/6 text-center">Role</ListHeader>
                    <ListHeader className="w-1/6 text-center">
                      Status
                    </ListHeader>
                    <ListHeader className="w-1/6 text-center">
                      Invitation Sent At
                    </ListHeader>
                    <ListHeader className="w-1/6 pr-4 text-right">
                      Actions
                    </ListHeader>
                  </tr>
                </thead>
                <tbody>
                  {allInvites.length > 0 ? (
                    allInvites.map((invite: Invite) => (
                      <tr
                        key={invite._id}
                        className="border-b border-msGray-5 hover:bg-msGray-6"
                      >
                        <td className="p-4">
                          <UserSubtext>{invite.email}</UserSubtext>
                        </td>
                        <td className="p-4 text-center">
                          <UserRoleBadge role={invite.role} />
                        </td>
                        <td className="p-4 text-center">
                          <span
                            className={`px-3 py-1 rounded-full text-smalldoge-4 font-medium ${
                              invite.status === InviteStatus.PENDING
                                ? 'bg-msGray-6 text-msGray-1'
                                : invite.status === InviteStatus.ACCEPTED
                                  ? 'bg-msGreen-4 text-msGreen-1'
                                  : 'bg-msRed-3 text-msRed-1'
                            }`}
                          >
                            {invite.status.charAt(0).toUpperCase() +
                              invite.status.slice(1)}
                          </span>
                        </td>
                        <td className="p-4 text-center">
                          <UserSubtext>
                            {formatJoinedDate(invite.updatedAt)}
                          </UserSubtext>
                        </td>
                        <td className="p-4">
                          <div className="flex justify-end">
                            <DropdownMenu modal={false}>
                              <DropdownMenuTrigger asChild>
                                <button
                                  className="p-1 transition-colors rounded hover:bg-msGray-6"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <MoreHorizontal
                                    size={16}
                                    className="transition-colors duration-200 text-msGray-3 hover:text-msGray-1"
                                  />
                                </button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                collisionPadding={10}
                                align="end"
                                className="w-38 text-smalldoge-3"
                              >
                                {invite.status === InviteStatus.PENDING ||
                                invite.status === InviteStatus.EXPIRED ? (
                                  <>
                                    <DropdownMenuItem
                                      onClick={() => handleResendInvite(invite)}
                                      disabled={isResendingInvite}
                                    >
                                      {isResendingInvite
                                        ? 'Resending...'
                                        : 'Resend Invitation'}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleCancelInvite(invite._id)
                                      }
                                      disabled={isDeletingInvite}
                                      className="text-msRed-1 focus:text-msRed-1"
                                    >
                                      {isDeletingInvite
                                        ? 'Canceling...'
                                        : 'Cancel Invitation'}
                                    </DropdownMenuItem>
                                  </>
                                ) : (
                                  <DropdownMenuItem
                                    onSelect={(e) => e.preventDefault()}
                                    className="focus:bg-transparent"
                                  >
                                    <span className="text-msGray-3">
                                      No actions available
                                    </span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr className="border-b border-msGray-5">
                      <td
                        colSpan={5}
                        className="p-6 text-center text-msGray-3 text-smalldoge-3"
                      >
                        No invitations found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {isLoadingUsers ? (
          <div className="flex items-center justify-center h-full py-10">
            <Loader />
          </div>
        ) : (
          <>
            {/* Users List */}
            <div className="mt-12">
              <h2 className="mb-4 px-4 text-lg font-semibold text-msBlack">
                Organization Members
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-msGray-5">
                      <ListHeader className="pl-4">Member</ListHeader>
                      <ListHeader className="text-center">Email</ListHeader>
                      <ListHeader className="text-center">Role</ListHeader>
                      <ListHeader className="pr-4 text-right">
                        Joined
                      </ListHeader>
                    </tr>
                  </thead>
                  <tbody>
                    {users && users.length > 0 ? (
                      users.map((user: User) => (
                        <tr
                          key={user._id}
                          className="transition-colors duration-100 border-b cursor-pointer border-msGray-5 hover:bg-msGray-7 hover:bg-msGray-6"
                          onClick={() => handleUserClick(user)}
                        >
                          <td className="p-4">
                            <div className="flex items-center">
                              <div className="flex items-center justify-center mr-4 rounded-full">
                                <Avatar
                                  size={40}
                                  url={user.avatar}
                                  name={`${user.firstName} ${user.lastName}`}
                                />
                              </div>
                              <div className="min-w-0 flex-1 max-w-[200px] md:max-w-[400px] lg:max-w-[600px] font-bold text-smalldoge-3 text-msBlack truncate">
                                {user.firstName} {user.lastName}
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <UserSubtext>{user.email}</UserSubtext>
                          </td>
                          <td className="p-4">
                            <UserRoleBadge role={user.role} />
                          </td>
                          <td className="p-4 text-right">
                            <UserSubtext>
                              {formatJoinedDate(user.joinedDate)}
                            </UserSubtext>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={4}
                          className="p-6 text-center text-msGray-3 text-smalldoge-3"
                        >
                          No organization members found
                          {searchTerm && ' matching your search'}.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>

      <InviteUserDrawer
        isOpen={isInviteDrawerOpen}
        onClose={() => setIsInviteDrawerOpen(false)}
      />

      <EditUserDrawer
        isOpen={isUserDetailsDrawerOpen}
        onClose={() => {
          setIsUserDetailsDrawerOpen(false);
          setSelectedUser(null); // Clear selected user when closing edit drawer
        }}
        user={selectedUser}
      />
    </div>
  );
}
