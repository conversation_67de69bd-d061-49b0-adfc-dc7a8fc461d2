import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { formatDistanceToNow } from 'date-fns';
import { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { UserInput } from 'shared/inputs';
import { UserRole } from 'shared/types';
import { toast } from 'sonner';
import { z } from 'zod';

import {
  ButtonSecondary,
  ButtonDanger,
  Input,
  Label,
  Separator,
  UpdatePasswordDrawer,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components';
import { Avatar } from '@/components/common/Avatar';
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { ImageInput } from '@/components/common/ImageInput';
import { useAuth } from '@/contexts/AuthContext';
import {
  updateUserRequest,
  deleteProfileRequest,
  cancelProfileDeletionRequest,
} from '@/helpers/requests';
import { cn } from '@/lib/utils';

const editUserSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
  avatar: z.string().nullable().optional(),
  profileImageFile: z.instanceof(File).nullable().optional(),
});

type EditUserFormData = z.infer<typeof editUserSchema>;

// Local Typography Components
function TypographySettingsLabel({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-smalldoge-3 font-bold text-msGray-3', className)}>
      {children}
    </p>
  );
}

function TypographySettingsValue({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p className={cn('text-smalldoge-3 font-bold text-msGray-2', className)}>
      {children}
    </p>
  );
}

function TypographySettingsSectionTitle({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <h2 className={cn('text-smalldoge-2 font-bold', className)}>{children}</h2>
  );
}

export function ProfileSettingsPage() {
  const queryClient = useQueryClient();
  const { user, logout, refreshUserData } = useAuth();
  const [isUpdatePasswordDrawerOpen, setIsUpdatePasswordDrawerOpen] =
    useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletePassword, setDeletePassword] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  const { mutate: deleteProfile, isPending: isDeletingProfile } = useMutation({
    mutationFn: deleteProfileRequest,
    onSuccess: async () => {
      toast.success('Profile scheduled for deletion');
      setDeletePassword('');
      setIsDeleteDialogOpen(false);
      // Refresh user data to show the deletion countdown
      await refreshUserData();
    },
    onError: (error: AxiosError) => {
      const message =
        (error?.response?.data as { message: string })?.message ||
        'Failed to schedule profile deletion';
      toast.error(message);
      setDeletePassword('');
    },
  });

  const cancelDeletionMutation = useMutation({
    mutationFn: cancelProfileDeletionRequest,
    onSuccess: async () => {
      toast.success('Profile deletion canceled successfully');
      // Refresh user data to hide the deletion countdown
      await refreshUserData();
    },
    onError: (error: AxiosError) => {
      const message =
        (error?.response?.data as { message: string })?.message ||
        'Failed to cancel profile deletion';
      toast.error(message);
    },
  });

  const handleDeleteProfile = () => {
    if (!deletePassword.trim()) {
      toast.error('Password is required');
      return;
    }
    deleteProfile(deletePassword);
  };

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { isDirty, errors },
  } = useForm<EditUserFormData>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      avatar: null,
      profileImageFile: null,
    },
  });

  const watchedAvatar = watch('avatar');

  useEffect(() => {
    if (user) {
      reset({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        avatar: user.avatar || null,
        profileImageFile: null,
      });
    }
  }, [user, reset]);

  const { mutate: updateUser, isPending: isUpdatingUser } = useMutation({
    mutationFn: (dto: Partial<UserInput>) => {
      if (!user) throw new Error('User not found');
      return updateUserRequest(user._id, dto);
    },
    onSuccess: async () => {
      // Refresh the auth context to get updated user data
      await refreshUserData();
      queryClient.invalidateQueries({ queryKey: ['organizationUsers'] });
      toast.success('Profile updated successfully');
      setIsEditing(false);
    },
    onError: (error: AxiosError) => {
      const message =
        (error?.response?.data as { message: string })?.message ||
        'Failed to update profile';
      toast.error(message);
    },
  });

  const handleImageUpdate = (file: File | null, previewUrl: string | null) => {
    setValue('profileImageFile', file, { shouldDirty: true });
    setValue('avatar', previewUrl, { shouldDirty: true });
  };

  const onSubmit = (data: EditUserFormData) => {
    if (!user) return;

    const dto: Partial<UserInput> = {
      firstName: data.firstName,
      lastName: data.lastName,
    };

    if (data.profileImageFile) {
      dto.avatar = data.profileImageFile;
    } else {
      dto.avatar = data.avatar;
    }

    if (data.avatar === null && !data.profileImageFile && user.avatar) {
      dto.avatar = null;
    }

    updateUser(dto);
  };

  return (
    <div className="flex flex-col max-w-xl gap-6 px-4 py-10 mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-smalldoge-2 font-bold">Profile Settings</h1>
        {!isEditing && (
          <ButtonSecondary onClick={() => setIsEditing(true)}>
            Edit Profile
          </ButtonSecondary>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 gap-y-6">
          {/* Profile Picture */}
          <div className="flex flex-col items-start self-start">
            <TypographySettingsLabel>Profile picture</TypographySettingsLabel>
            <span className="text-smalldoge-5 text-msGray-3">
              Recommended size is 256x256px
            </span>
          </div>
          <div className="self-start">
            {isEditing ? (
              <ImageInput
                url={watchedAvatar}
                name={`${user?.firstName} ${user?.lastName}`}
                onImageUpdate={handleImageUpdate}
                error={
                  errors.avatar?.message || errors.profileImageFile?.message
                }
              />
            ) : (
              <Avatar
                url={user?.avatar}
                name={`${user?.firstName} ${user?.lastName}`}
                size={64}
              />
            )}
          </div>

          {/* First Name */}
          <TypographySettingsLabel>First name</TypographySettingsLabel>
          {isEditing ? (
            <Controller
              name="firstName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter first name"
                  className="max-w-sm"
                  error={errors.firstName?.message}
                />
              )}
            />
          ) : (
            <TypographySettingsValue>
              {user?.firstName || '-'}
            </TypographySettingsValue>
          )}

          {/* Last Name */}
          <TypographySettingsLabel>Last name</TypographySettingsLabel>
          {isEditing ? (
            <Controller
              name="lastName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter last name"
                  className="max-w-sm"
                  error={errors.lastName?.message}
                />
              )}
            />
          ) : (
            <TypographySettingsValue>
              {user?.lastName || '-'}
            </TypographySettingsValue>
          )}
        </div>

        {/* Form Actions */}
        {isEditing && (
          <div className="flex justify-end gap-2 mt-6">
            <ButtonSecondary
              type="button"
              onClick={() => {
                setIsEditing(false);
                reset();
              }}
              disabled={isUpdatingUser}
            >
              Cancel
            </ButtonSecondary>
            <ButtonSecondary
              type="submit"
              disabled={!isDirty || isUpdatingUser}
            >
              {isUpdatingUser ? 'Saving...' : 'Save Changes'}
            </ButtonSecondary>
          </div>
        )}
      </form>

      <Separator className="my-6" />

      {/* Login & Password Section */}
      <div className="flex flex-col gap-3">
        <h2 className="text-smalldoge-2 font-bold">Login & Password</h2>
        <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 gap-y-4">
          <TypographySettingsLabel>Your email</TypographySettingsLabel>
          <TypographySettingsValue>
            {user?.email || '-'}
          </TypographySettingsValue>

          <TypographySettingsLabel>Password</TypographySettingsLabel>
          <div className="flex items-center justify-between">
            <TypographySettingsValue>********</TypographySettingsValue>
            <ButtonPrimary
              variant="link"
              onClick={() => setIsUpdatePasswordDrawerOpen(true)}
            >
              Update password
            </ButtonPrimary>
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      {/* Account Deletion Countdown - Show when deletion is scheduled */}
      {user?.deletionScheduledDate && (
        <div className="flex flex-col gap-3 mt-20">
          <TypographySettingsSectionTitle>
            Account Status
          </TypographySettingsSectionTitle>
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-msRed-1 animate-pulse"></div>
              <TypographySettingsValue className="text-msRed-1">
                Account deletion scheduled
              </TypographySettingsValue>
            </div>
            <div className="p-3 border rounded-lg bg-msRed-3/10 border-msRed-2/20">
              <p className="mb-2 font-semibold text-smalldoge-3 text-msRed-1">
                Your account will be permanently deleted{' '}
                {formatDistanceToNow(new Date(user.deletionScheduledDate), {
                  addSuffix: true,
                })}
              </p>
              <p className="mb-2 text-smalldoge-5 text-msGray-3">
                Deletion date:{' '}
                {new Date(user.deletionScheduledDate).toLocaleDateString(
                  'en-US',
                  {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  },
                )}
              </p>
              <p className="mb-3 text-smalldoge-5 text-msGray-3">
                After the deletion date, all your data will be permanently
                removed and cannot be recovered.
              </p>
              <ButtonSecondary
                onClick={() => cancelDeletionMutation.mutate()}
                disabled={cancelDeletionMutation.isPending}
                className="w-fit"
              >
                {cancelDeletionMutation.isPending
                  ? 'Canceling...'
                  : 'Cancel Deletion'}
              </ButtonSecondary>
            </div>
          </div>
        </div>
      )}

      {/* Danger Zone Section */}
      {!user?.deletionScheduledDate && (
        <div className="flex flex-col gap-3 mt-20">
          <h2 className="text-smalldoge-2 font-bold">Danger zone</h2>
          <div className="p-4 rounded-lg bg-msGray-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <TypographySettingsValue>
                  Delete profile
                </TypographySettingsValue>
                <span className="text-smalldoge-5 text-msGray-3">
                  Deletes this users profile permanently
                </span>
              </div>
              <Dialog
                open={isDeleteDialogOpen}
                onOpenChange={(open) => {
                  setIsDeleteDialogOpen(open);
                  if (!open) {
                    setDeletePassword('');
                  }
                }}
              >
                <DialogTrigger asChild>
                  <ButtonDanger>Delete profile</ButtonDanger>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Schedule Profile Deletion?</DialogTitle>
                    <DialogDescription>
                      <p className="mb-2">
                        Your profile will be scheduled for deletion in 7 days.
                        During this period:
                      </p>
                      <ul className="pl-5 mb-2 list-disc">
                        <li>You can continue using your account normally</li>
                        <li>
                          Your data will remain accessible to administrators
                        </li>
                        <li>You can contact support to cancel the deletion</li>
                      </ul>
                      <p className="font-semibold text-msRed-1">
                        After 7 days, all your data will be permanently deleted
                        and cannot be recovered.
                      </p>
                    </DialogDescription>
                  </DialogHeader>
                  <div className="my-4">
                    <Label
                      label="Confirm your password"
                      labelProps={{ htmlFor: 'delete-password' }}
                    />
                    <Input
                      id="delete-password"
                      type="password"
                      value={deletePassword}
                      onChange={(e) => setDeletePassword(e.target.value)}
                      placeholder="Enter your password"
                      className="mt-2"
                    />
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <ButtonSecondary>Cancel</ButtonSecondary>
                    </DialogClose>
                    <ButtonDanger
                      variant="pink"
                      onClick={handleDeleteProfile}
                      disabled={isDeletingProfile || !deletePassword.trim()}
                    >
                      {isDeletingProfile
                        ? 'Scheduling...'
                        : 'Schedule Deletion'}
                    </ButtonDanger>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      )}

      <UpdatePasswordDrawer
        isOpen={isUpdatePasswordDrawerOpen}
        onClose={() => setIsUpdatePasswordDrawerOpen(false)}
      />
    </div>
  );
}
