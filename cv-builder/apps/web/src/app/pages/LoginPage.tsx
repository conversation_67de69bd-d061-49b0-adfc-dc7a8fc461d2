import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useForm } from 'react-hook-form';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { toast } from 'sonner';
import { z } from 'zod';

import { useAuth } from '../contexts/AuthContext';
import { NAVIGATE_PATH } from '../helpers/constants';

import { Button } from '@/components/common/Button';
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Input } from '@/components/common/Input';

export function LoginPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const inviteToken = searchParams.get('token');
  const visualization = searchParams.get('visualization');
  const { login } = useAuth();

  // In test environment, only show sign up button if visualization param is present
  const shouldShowSignUp =
    import.meta.env.VITE_DEPLOYMENT_TYPE !== 'test' || visualization;

  const loginSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(1, 'Password is required'),
  });

  type LoginFormValues = z.infer<typeof loginSchema>;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    clearErrors,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (dto: SignInDto) => {
      // Add invite token to the request if present
      const loginData = inviteToken ? { ...dto, token: inviteToken } : dto;
      return login(loginData);
    },
    onSuccess: (data) => {
      toast.success(data.message || 'Logged in successfully');
      navigate(NAVIGATE_PATH.home);
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      setError('password', {
        type: 'manual',
        message: message || 'Invalid email or password',
      });
    },
  });

  const onSubmit = (data: LoginFormValues) => {
    clearErrors();
    mutation.mutate(data);
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      {/* Header elements */}
      <div className="absolute top-4 left-4 right-4 md:top-8 md:left-8 md:right-8 flex items-center justify-between">
        <img
          src="/images/logo-with-text.png"
          alt="CV Inventory"
          className="w-48"
        />
        {shouldShowSignUp && (
          <Link to={NAVIGATE_PATH.signUp}>
            <Button variant="default" size="sm">
              Sign Up
            </Button>
          </Link>
        )}
      </div>

      <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-foreground">Login</h1>
          <p className="text-sm text-muted-foreground">
            Enter your email and password to login to your account
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              type="email"
              placeholder="<EMAIL>"
              className="w-full"
              {...register('email')}
              disabled={mutation.isPending}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-destructive">
                {errors.email.message}
              </p>
            )}
          </div>
          <div>
            <Input
              type="password"
              placeholder="Password"
              className="w-full"
              {...register('password')}
              disabled={mutation.isPending}
            />
            {errors.password && (
              <p className="mt-1 text-sm text-destructive">
                {errors.password.message}
              </p>
            )}
          </div>
          <div className="flex items-center justify-end">
            <Link
              to={NAVIGATE_PATH.passwordReset}
              className="text-sm underline text-muted-foreground hover:text-foreground"
            >
              Forgot password?
            </Link>
          </div>

          <ButtonPrimary
            type="submit"
            className="w-full rounded-md"
            disabled={mutation.isPending}
          >
            {mutation.isPending
              ? 'Logging in...'
              : inviteToken
                ? 'Log in and accept invite'
                : 'Log in'}
          </ButtonPrimary>
        </form>
      </div>
    </div>
  );
}
