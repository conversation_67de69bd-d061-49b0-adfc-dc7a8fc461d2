import { AxiosError } from 'axios';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { UpdatePasswordDto } from 'shared/dto/auth/update-password.dto';
import { SortOrder } from 'shared/dto/common.dto';
import { GetCvsSortBy } from 'shared/dto/cv/get-cvs.dto';
import { GetMembersSortBy } from 'shared/dto/members/get-members.dto';
import {
  CvPreferencesInput,
  CreateMemberInput,
  UpdateCvInput,
  UpdateMemberInput,
  UserInput,
  UpdateCvSectionsInput,
} from 'shared/inputs';
import {
  Paging,
  MemberSource,
  CvProfileFilter,
  PaginatedMuchskillsMembers,
  Cv,
  PaginatedMembers,
  UserRole,
  OrganizationFullInfo,
  Member,
  CvStatus,
  Skill,
  Certification,
  Message,
  Invite,
  UserData,
  Organization,
  Customer,
} from 'shared/types';

import api from '../api';

// Note: loginRequest is now handled by AuthContext
// This function is kept for backward compatibility but should not be used
export const loginRequest = async (dto: SignInDto, token?: string) => {
  const response = await api.post('/login', dto, {
    params: token ? { token } : undefined,
  });
  return response.data;
};

export const getMembersRequest = async (
  paging: Paging,
  search?: string,
  sortBy?: GetMembersSortBy,
  sortOrder?: SortOrder,
) => {
  const response = await api.get('/members', {
    params: {
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
      ...(search && { search }),
      ...(sortBy && { sortBy }),
      ...(sortOrder && { sortOrder }),
    },
  });

  return response.data as PaginatedMembers;
};

export const getMemberByIdRequest = async (memberId: string) => {
  const response = await api.get(`/members/${memberId}`);

  return response.data as Member;
};

export const getMembersBySourceRequest = async (
  importType: MemberSource,
  paging: Paging,
  search?: string,
) => {
  const response = await api.get('/members', {
    params: {
      source: importType,
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
      ...(search && { search }),
    },
  });
  return response.data;
};

export const getMuchskillsMembers = async ({
  orgId,
  paging,
  searchValue,
  filter,
}: {
  orgId: string;
  paging: Paging;
  searchValue: string;
  filter: CvProfileFilter;
}) => {
  const response = await api.post(`/muchskills/members/${orgId}`, {
    page: paging.page,
    itemsPerPage: paging.itemsPerPage,
    searchValue,
    filter,
  });
  return response.data as PaginatedMuchskillsMembers;
};

export const syncMuchskillsMembers = async (orgId: string) => {
  await api.post(`/muchskills/members-sync/${orgId}`);
};

export const createMemberFromMuchskills = async ({
  orgId,
  email,
}: {
  orgId: string;
  email: string;
}) => {
  await api.post(`/muchskills/create-member/${orgId}/${email}`);
};

export const createMemberRequest = async (
  dto: CreateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post('/members/create', dto);
    const memberId = response.data._id;

    if (avatar && memberId) {
      const formData = new FormData();
      formData.append('avatar', avatar);

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateMemberRequest = async (
  memberId: string,
  dto: UpdateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post(`/members/update/${memberId}`, dto);

    if (avatar !== undefined) {
      const formData = new FormData();

      if (avatar) {
        formData.append('avatar', avatar);
      } else {
        formData.append('removeAvatar', 'true');
      }

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const deleteMemberRequest = async (memberId: string): Promise<void> => {
  const response = await api.delete(`/members/${memberId}`);
  return response.data;
};

export const regenerateCvRequest = async (
  id: string,
  aiQuery: string,
  messages?: Message[],
) => {
  const response = await api.post(
    `/cvs/regenerate/${id}`,
    {
      query: aiQuery,
      ...(messages && { messages }),
    },
    {
      timeout: 60000, // 60 seconds timeout for AI processing
    },
  );
  return response.data;
};

export const createCvRequest = async (
  memberId: string,
  dto: CvPreferencesInput,
) => {
  try {
    const response = await api.post(`/cvs/create/${memberId}`, dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateCvRequest = async (cvId: string, dto: UpdateCvInput) => {
  try {
    const response = await api.post(`/cvs/update/${cvId}`, dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateCvSectionsRequest = async (
  cvId: string,
  sections: UpdateCvSectionsInput,
) => {
  try {
    const response = await api.post(`/cvs/update-sections/${cvId}`, sections);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const duplicateCvRequest = async (cvId: string) => {
  const response = await api.post(`/cvs/duplicate/${cvId}`);
  return response.data;
};

export const deleteCvRequest = async (cvId: string) => {
  const response = await api.delete(`/cvs/delete/${cvId}`);
  return response.data;
};

export const getCvsByMemberRequest = async (memberId: string) => {
  const response = await api.get(`/cvs/byMember/${memberId}`);
  return response.data as Cv[];
};

export const getCvById = async (cvId: string) => {
  const response = await api.get(`/cvs/byId/${cvId}`);
  return response.data as Cv;
};

export const getMemberBase64Avatar = async (memberId: string) => {
  const response = await api.get(`/cvs/getMemberBase64Avatar/${memberId}`);
  return response.data as string;
};

export const signUpRequest = async (dto: SignUpDto, token?: string) => {
  const response = await api.post('/sign-up', dto, {
    params: token ? { token } : undefined,
  });
  return response.data;
};

export const requestPasswordResetEmail = async (email: string) => {
  const response = await api.post('/reset-password-request', { email });
  return response.data;
};

export const resetPasswordRequest = async (dto: ResetPasswordDto) => {
  const response = await api.post('/reset-password', dto);
  return response.data;
};

export const updatePasswordRequest = async (dto: UpdatePasswordDto) => {
  const response = await api.post('/update-password', dto);
  return response.data;
};

export const deleteProfileRequest = async (password: string): Promise<void> => {
  const response = await api.delete('/users/me', {
    data: { password },
  });
  return response.data;
};

export const cancelProfileDeletionRequest = async (): Promise<UserData> => {
  const response = await api.post('/users/me/cancel-deletion');
  return response.data;
};

export const cancelOrganizationDeletionRequest =
  async (): Promise<Organization> => {
    const response = await api.post('/organization/cancel-deletion');
    return response.data;
  };

// Note: logoutRequest is now handled by AuthContext
// This function is kept for backward compatibility but should not be used
export const logoutRequest = async () => {
  const response = await api.post('/sign-out');
  return response.data;
};

export const connectToMuchskills = async (orgId: string, token: string) => {
  try {
    const response = await api.post(
      `/muchskills/connect/${orgId}`,
      {},
      {
        headers: {
          'ms-token': token,
        },
      },
    );

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const disconnectMuchskills = async (orgId: string) => {
  const response = await api.post(`/muchskills/disconnect/${orgId}`);
  return response.data;
};

export const getOrganizationUsersRequest = async (searchTerm?: string) => {
  const response = await api.get('/organization/users', {
    params: searchTerm ? { search: searchTerm } : undefined,
  });
  return response.data;
};

export const getOrganizationInvitesRequest = async (): Promise<Invite[]> => {
  const response = await api.get('/invites');
  return response.data;
};

export const createOrganizationInviteRequest = async (dto: {
  email: string;
  role: UserRole;
  name?: string;
}) => {
  const response = await api.post('/invites', dto);
  return response.data;
};

export const resendOrganizationInviteRequest = async (dto: {
  email: string;
  role: UserRole;
}) => {
  const response = await api.post('/invites', dto);
  return response.data;
};

export const deleteOrganizationInviteRequest = async (inviteId: string) => {
  const response = await api.delete(`/invites/${inviteId}`);
  return response.data;
};

export const deleteOrganizationRequest = async (
  password: string,
): Promise<void> => {
  const response = await api.delete('/organization', {
    data: { password },
  });
  return response.data;
};

export const updateUserRequest = async (
  userId: string,
  dto: Partial<UserInput>,
) => {
  // Separate avatar if it's a File
  const userData = { ...dto };
  let avatarFile: File | null = null;

  if (dto.avatar && dto.avatar instanceof File) {
    avatarFile = dto.avatar;
    delete userData.avatar; // Don't send File object in JSON payload
  }

  const response = await api.patch(`/users/${userId}`, userData);

  // If there's an avatar file, upload it separately
  // This assumes a backend endpoint like /users/{userId}/avatar exists
  // and can handle FormData. You might need to adjust this based on your API.
  if (avatarFile) {
    const formData = new FormData();
    formData.append('avatar', avatarFile);
    // TODO: Confirm or create this endpoint on the backend
    await api.post(`/users/${userId}/avatar`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  }

  return response.data;
};

export const getAllOrganizationsRequest = async () => {
  const response = await api.get('/organization/all');
  return response.data;
};

export const getCurrentUserRequest = async () => {
  const response = await api.get('/me');
  return response.data;
};

export const setActiveOrganizationRequest = async (organizationId: string) => {
  return api.post(`/organization/set-active/${organizationId}`);
};

export const getOrganizationRequest = async () => {
  const response = await api.get('/organization');
  return response.data;
};

export const createOrganizationRequest = async (dto: { name: string }) => {
  try {
    const response = await api.post('/organization', dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const acceptInviteRequest = async (token: string) => {
  const response = await api.post('/invites/accept', { token });
  return response.data;
};

export const getInviteDetailsRequest = async (token: string) => {
  const response = await api.get(`/invites/details/${token}`);
  return response.data;
};

export const createStripePortalSessionRequest = async (
  dto?: Record<string, never>,
) => {
  const response = await api.post('/stripe/create-portal-session', dto || {});
  return response.data as { url: string };
};

export const getStripeProductsRequest = async () => {
  const response = await api.get('/stripe/products');
  return response.data;
};

export const getOrganizationFullInfoRequest = async () => {
  const response = await api.get('/organization/full-info');
  return response.data as OrganizationFullInfo;
};

export const updateOrganizationRequest = async (
  dto: {
    name?: string;
    description?: string;
    website?: string;
    timezone?: string;
    locations?: string[];
    photo?: string | null;
  },
  photoFile?: File | null,
) => {
  try {
    const response = await api.put('/organization', dto);

    if (photoFile !== undefined) {
      const formData = new FormData();

      if (photoFile) {
        formData.append('photo', photoFile);
      } else {
        formData.append('removePhoto', 'true');
      }

      await api.post('/organization/photo-update', formData);
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const getCustomersRequest = async () => {
  const response = await api.get('/customers');
  return response.data as Customer[];
};

export const createCustomerRequest = async (dto: { name: string }) => {
  try {
    const response = await api.post('/customers', dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const updateCustomerRequest = async (
  customerId: string,
  dto: { name: string },
) => {
  try {
    const response = await api.put(`/customers/${customerId}`, dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const deleteCustomerRequest = async (customerId: string) => {
  try {
    const response = await api.delete(`/customers/${customerId}`);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const getCvsRequest = async (
  paging: Paging,
  search?: string,
  sortBy?: GetCvsSortBy,
  sortOrder?: SortOrder,
  status?: CvStatus,
): Promise<{ cvs: Cv[]; total: number }> => {
  const response = await api.get('/cvs', {
    params: {
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
      search,
      sortBy,
      sortOrder,
      status: status || 'draft',
    },
  });

  return response.data;
};

export const searchSkillsRequest = async (
  searchValue: string,
  orgId?: string,
  skillsToExclude?: string[],
) => {
  try {
    const response = await api.post('/muchskills/skills/search', {
      searchValue,
      orgId,
      skillsToExclude,
    });

    return response.data as Skill[];
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};

export const searchCertificatesRequest = async (
  searchValue: string,
  certificatesToExclude?: string[],
) => {
  try {
    const response = await api.post('/muchskills/certificates/search', {
      searchValue,
      certificatesToExclude,
    });

    return response.data as Certification[];
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }
    throw new Error(error.message);
  }
};
