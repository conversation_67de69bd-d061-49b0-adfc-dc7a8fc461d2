import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import { NAVIGATE_PATH } from '@/helpers/constants';
import { getQueryParams, clearQueryParams } from '@/helpers/urlUtils';

/**
 * Component that handles toast messages from URL query parameters
 * and redirects to the home page if needed
 */
export function ToastHandler() {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const params = getQueryParams();

    // Check if there's a message parameter
    if (params.message) {
      // Show the success toast
      toast.success(params.message);

      // Clear the query parameters
      clearQueryParams();

      // If we're at the root path, redirect to home
      if (location.pathname === '/') {
        navigate(NAVIGATE_PATH.home);
      }
    }
  }, [location, navigate]);

  return null;
}
