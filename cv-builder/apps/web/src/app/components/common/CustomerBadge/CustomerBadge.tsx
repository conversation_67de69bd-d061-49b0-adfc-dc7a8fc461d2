import { cn } from '@/lib/utils';

interface CustomerBadgeProps {
  name?: string;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
}

export function CustomerBadge({
  name,
  className,
  onClick,
}: CustomerBadgeProps) {
  return (
    <div
      className={cn(
        'w-fit bg-msGray-6 rounded-[100px] px-2 truncate min-w-0 text-msBlue-1 text-smalldoge-3',
        className,
        onClick && 'cursor-pointer hover:bg-msGray-5',
      )}
      onClick={onClick}
      title={name || 'No Client'}
    >
      {name || 'No Client'}
    </div>
  );
}
