import { memo } from 'react';
import { LanguagesData, SectionData } from 'shared/types';

import { TagsInput } from '../../../../components';

import { FormReducerAction, ReducerActionType } from '@/pages/CVEditPage';

interface LanguagesFormProps {
  sectionId: string;
  data: LanguagesData;
  onDataChange: React.Dispatch<FormReducerAction>;
}

export const LanguagesForm = memo(
  ({ sectionId, data, onDataChange }: LanguagesFormProps) => {
    function handleChange(data: SectionData) {
      onDataChange({
        type: ReducerActionType.updateField,
        sectionId: sectionId,
        data,
      });
    }

    return (
      <div className="flex flex-col space-y-5 pt-4 p-px">
        <TagsInput
          id="langs"
          value={data.languages}
          onValueChange={(val) => {
            handleChange({
              ...data,
              languages: val,
            });
          }}
        />
      </div>
    );
  },
);
