import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { UserInput } from 'shared/inputs';
import { UserRole, UserData } from 'shared/types';
import { z } from 'zod';

import { ButtonSecondary } from '@/components/common/ButtonSecondary';
import { Drawer } from '@/components/common/Drawer';
import { FormDropdown } from '@/components/common/FormDropdown';
import { ImageInput } from '@/components/common/ImageInput';
import { Input } from '@/components/common/Input';
import { useAuth } from '@/contexts/AuthContext';
import { updateUserRequest } from '@/helpers/requests';
import { cn } from '@/lib/utils';

const editUserSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
  avatar: z.string().nullable().optional(),
  profileImageFile: z.instanceof(File).nullable().optional(),
});
type EditUserFormData = z.infer<typeof editUserSchema>;

interface EditUserDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserData | null;
}

const roleOptions = [
  { value: UserRole.OWNER, label: 'Owner' },
  { value: UserRole.ADMIN, label: 'Admin' },
  { value: UserRole.MEMBER, label: 'Member' },
];

export function EditUserDrawer({ isOpen, onClose, user }: EditUserDrawerProps) {
  const queryClient = useQueryClient();
  const { user: currentUser, canManageUsers, refreshUserData } = useAuth();

  // Permission calculations
  const isEditingSelf = currentUser?._id === user?._id;
  const canEdit = isEditingSelf || canManageUsers;
  const canEditRole = canManageUsers && !isEditingSelf;
  const isViewOnly = !canEdit;

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { isDirty, errors },
  } = useForm<EditUserFormData>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      role: UserRole.MEMBER,
      avatar: null,
      profileImageFile: null,
    },
  });

  // const watchedRole = watch('role');
  const watchedAvatar = watch('avatar');
  // const watchedProfileImageFile = watch('profileImageFile'); // For direct file handling if needed

  useEffect(() => {
    if (user && isOpen) {
      reset({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role,
        avatar: user.avatar || null,
        profileImageFile: null, // Reset file on user change/drawer open
      });
    }
  }, [user, isOpen, reset]);

  // Prevent form changes in view-only mode
  useEffect(() => {
    if (isViewOnly && isDirty) {
      reset(); // Reset any accidental changes
    }
  }, [isViewOnly, isDirty, reset]);

  // Dynamic drawer title
  const drawerTitle = useMemo(() => {
    if (isEditingSelf) return 'Edit your profile';
    if (isViewOnly) return `View ${user?.firstName || 'User'}'s profile`;
    return 'Edit member access and details';
  }, [isEditingSelf, isViewOnly, user?.firstName]);

  const { mutate: updateUser, isPending: isUpdatingUser } = useMutation({
    mutationFn: ({
      userId,
      dto,
    }: {
      userId: string;
      dto: Partial<UserInput>;
    }) => updateUserRequest(userId, dto),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['organizationUsers'] });
      if (isEditingSelf) {
        await refreshUserData();
      }
      onClose();
      reset();
    },
  });

  const handleImageUpdate = (file: File | null, previewUrl: string | null) => {
    setValue('profileImageFile', file, { shouldDirty: true });
    setValue('avatar', previewUrl, { shouldDirty: true });
  };

  const onSubmit = (data: EditUserFormData) => {
    if (!user || isViewOnly) return;

    const dto: Partial<UserInput> = {
      firstName: data.firstName,
      lastName: data.lastName,
    };

    // Only include role if user can edit roles
    if (canEditRole && data.role !== undefined) {
      dto.role = data.role;
    }

    // Handle avatar update:
    // If a new file is selected, it takes precedence.
    // Otherwise, use the avatar URL (which might be null if cleared).
    if (data.profileImageFile) {
      dto.avatar = data.profileImageFile; // Assuming updateUserRequest can handle File
    } else {
      dto.avatar = data.avatar;
    }

    // Explicitly set avatar to null if it was cleared and no new file was uploaded
    if (data.avatar === null && !data.profileImageFile && user.avatar) {
      dto.avatar = null;
    }

    updateUser({ userId: user._id, dto });
  };

  // Available role options based on current user's permissions
  const availableRoleOptions = useMemo(() => {
    if (!canEditRole) return [];

    // Members can't edit roles at all
    if (currentUser?.role === UserRole.MEMBER) return [];

    // Admins can assign ADMIN/MEMBER but not OWNER
    if (currentUser?.role === UserRole.ADMIN) {
      return roleOptions.filter((option) => option.value !== UserRole.OWNER);
    }

    // Owners can assign any role
    return roleOptions;
  }, [canEditRole, currentUser?.role]);

  // const selectedRoleLabel = useMemo(() => {
  //   return (
  //     roleOptions.find((option) => option.value === watchedRole)?.label ||
  //     watchedRole
  //   );
  // }, [watchedRole]);

  // Show role field only if user can edit roles and has available options
  const showRoleField = canEditRole && availableRoleOptions.length > 0;

  const renderField = (
    label: string,
    children: React.ReactNode,
    isReadOnly = false,
  ) => (
    <div
      className={cn(
        'grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 py-2',
        isReadOnly && 'opacity-75',
      )}
    >
      <div className="font-bold text-smalldoge-3 text-msGray-3">{label}</div>
      <div>{children}</div>
    </div>
  );

  return (
    <Drawer
      active={isOpen}
      onClose={() => {
        onClose();
      }}
      title={drawerTitle}
      className="w-[680px]"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
        {renderField(
          'Profile photo',
          <ImageInput
            url={watchedAvatar}
            name={`${user?.firstName} ${user?.lastName}`}
            onImageUpdate={handleImageUpdate}
            isReadOnly={isViewOnly}
            error={
              !isViewOnly
                ? errors.avatar?.message || errors.profileImageFile?.message
                : undefined
            }
          />,
          isViewOnly,
        )}

        {renderField(
          'First Name',
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter first name"
                className="font-bold text-smalldoge-3 text-msBlack"
                isReadOnly={isViewOnly}
                error={!isViewOnly ? errors.firstName?.message : undefined}
              />
            )}
          />,
          isViewOnly,
        )}

        {renderField(
          'Last Name',
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter last name"
                className="font-bold text-smalldoge-3 text-msBlack"
                isReadOnly={isViewOnly}
                error={!isViewOnly ? errors.lastName?.message : undefined}
              />
            )}
          />,
          isViewOnly,
        )}

        {renderField(
          'Email',
          <Input
            value={user?.email}
            className="font-bold text-smalldoge-3 text-msBlack"
            disabled
          />,
        )}

        {renderField(
          'Role',
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <FormDropdown
                value={field.value}
                onValueChange={(value) =>
                  setValue('role', value as UserRole, { shouldDirty: true })
                }
                options={roleOptions}
                selectableOptions={availableRoleOptions}
                className="pl-2"
                placeholder="Select role"
                isReadOnly={isViewOnly || !showRoleField}
                error={
                  !isViewOnly && showRoleField
                    ? errors.role?.message
                    : undefined
                }
                disabled={isViewOnly || !showRoleField}
              />
            )}
          />,
          isViewOnly || !showRoleField,
        )}
        <div className="flex justify-end p-6 gap-x-2">
          <ButtonSecondary
            variant="ghost"
            onClick={() => {
              onClose();
            }}
            disabled={isUpdatingUser}
            type="button" // Prevent form submission
          >
            {isViewOnly ? 'Close' : 'Cancel'}
          </ButtonSecondary>

          {!isViewOnly && (
            <ButtonSecondary
              variant="white"
              type="submit"
              disabled={isUpdatingUser || !isDirty || !user}
            >
              {isUpdatingUser ? 'Saving...' : 'Save'}
            </ButtonSecondary>
          )}
        </div>
      </form>
    </Drawer>
  );
}
