import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { LogOut, UserPen } from 'lucide-react';
import { Link } from 'react-router-dom';
import { DEFAULT_ORG_IMAGE } from 'shared/constants';
import { StripePlanMapping } from 'shared/types';

import {
  // BundledIcon,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  Icon,
} from '@/components';
import { PLANS } from '@/components/Billing/plans.constants';
import { useAuth } from '@/contexts/AuthContext';
import { SETTINGS_PATH } from '@/helpers/constants';
import { getOrganizationFullInfoRequest } from '@/helpers/requests';
import { cn } from '@/lib/utils';

const menuCategories = [
  // {
  //   title: 'Workspace',
  //   items: [
  //     { title: 'People', icon: 'projDraft', link: NAVIGATE_PATH.home },
  //     { title: 'Integrations', icon: 'team', link: NAVIGATE_PATH.home },
  //     { title: 'Billing', icon: 'table', link: NAVIGATE_PATH.home },
  //     { title: 'CV Templates', icon: 'table', link: NAVIGATE_PATH.home },
  //     { title: 'Drafts', icon: 'projDraft', link: NAVIGATE_PATH.home },
  //   ],
  // },
  {
    title: 'Your Account',
    items: [
      // { title: 'Reports', icon: 'sectorChart', link: NAVIGATE_PATH.home },
      // { title: 'Settings', icon: 'gear', link: NAVIGATE_PATH.settings },
    ],
  },
];

interface WorkspaceDropdownProps {
  container: HTMLElement | null;
}

export function WorkspaceDropdown({ container }: WorkspaceDropdownProps) {
  const queryClient = useQueryClient();
  const { user, organization, logout, switchOrganization } = useAuth();

  // Fetch organization full info to get member count
  const { data: organizationFullInfo } = useQuery({
    queryKey: ['organizationFullInfo'],
    queryFn: getOrganizationFullInfoRequest,
    enabled: !!organization?._id,
  });

  const logoutMutation = useMutation({
    mutationFn: logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });

  const orgImage = organization?.photo || DEFAULT_ORG_IMAGE;
  const orgName = organization?.name || 'Organization';

  const planTier = organization?.planTier as StripePlanMapping;
  const planName = planTier ? PLANS[planTier]?.name : 'Free Plan';
  const memberCount = organizationFullInfo?.totalMembers ?? 0;

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleSetActiveOrganization = (orgId: string) => {
    if (!orgId) {
      console.error(
        'Cannot switch to organization: organization ID is undefined',
      );
      return;
    }
    switchOrganization(orgId);
  };

  const availableOrganizations = user?.availableOrganizations || [];

  const organizationInfoClassName = 'flex flex-col flex-1';

  const OrganizationInfo = ({ className }: { className?: string }) => (
    <div className={cn(organizationInfoClassName, className)}>
      <div
        className="font-bold text-smalldoge-4 max-w-[100px] truncate"
        title={orgName}
      >
        {orgName}
      </div>
      <div className="flex items-center gap-1 text-[10px] text-msGray-3">
        <span>{planName}</span>
        <div className="w-0.5 h-0.5 bg-msGray-3 rounded-full" />
        <span>{memberCount} members</span>
      </div>
    </div>
  );

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <div className="flex space-x-2 cursor-pointer">
          <img className="size-8 rounded-[4px]" src={orgImage} alt="org_logo" />
          <div className="flex items-center">
            <div
              className="font-bold text-smalldoge-4 max-w-[140px] truncate"
              title={orgName}
            >
              {orgName}
            </div>
            <Icon source="down" size={16} />
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        portalProps={{ container: container }}
        collisionPadding={10}
        className="w-[200px]"
      >
        {availableOrganizations.length <= 1 ? (
          <div className="flex space-x-2 px-2 py-1.5 w-[200px]">
            <img
              className="size-8 rounded-[4px]"
              src={orgImage}
              alt="org_logo"
            />
            <OrganizationInfo className="flex-1" />
          </div>
        ) : (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <div className="flex space-x-2 cursor-pointer">
                <img
                  className="size-8 rounded-[4px]"
                  src={orgImage}
                  alt="org_logo"
                />
                <OrganizationInfo className="flex-1" />
              </div>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent
                className="w-[200px]"
                collisionPadding={20}
                sideOffset={2}
                alignOffset={-5}
                collisionBoundary={document.body}
              >
                {availableOrganizations.map((orgMembership) => {
                  return (
                    <DropdownMenuItem
                      key={orgMembership.orgId}
                      onClick={() =>
                        handleSetActiveOrganization(orgMembership.orgId)
                      }
                      className={
                        orgMembership.orgId === organization?._id
                          ? 'bg-msGray-6'
                          : ''
                      }
                    >
                      <div className="flex space-x-2 cursor-pointer">
                        <img
                          className="size-8 rounded-[4px]"
                          src={orgMembership.photo || DEFAULT_ORG_IMAGE}
                          alt="org_logo"
                        />
                        <div className="flex items-center">
                          <span className="font-bold text-smalldoge-4 max-w-[140px] truncate">
                            {orgMembership.name}
                          </span>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        )}
        {menuCategories.map((category) => (
          <div key={category.title}>
            <DropdownMenuLabel>
              <div className="flex items-center space-x-2">
                <span className="text-smalldoge-5 text-msGray-3">
                  {category.title}
                </span>
                <span className="flex-grow h-px bg-msGray-5" />
              </div>
            </DropdownMenuLabel>
            {/* {category.items.map((item) => (
              <DropdownMenuItem key={item.title}>
                <Link
                  id={item.title}
                  to={item.link}
                  className="flex items-center space-x-2"
                >
                  <Icon source={item.icon as BundledIcon} size={16} />
                  <span className="font-bold text-smalldoge-4">
                    {item.title}
                  </span>
                </Link>
              </DropdownMenuItem>
            ))} */}
            {/* Always show Profile Settings and logout option for Your Account category */}
            {category.title === 'Your Account' && (
              <>
                <DropdownMenuItem asChild>
                  <Link
                    to={SETTINGS_PATH.profileSettings}
                    className="flex items-center"
                  >
                    <UserPen size={16} />
                    <span className="font-bold text-smalldoge-4">
                      Profile Settings
                    </span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleLogout}
                  disabled={logoutMutation.isPending}
                >
                  <div className="flex items-center space-x-2">
                    <LogOut size={16} />
                    <span className="font-bold text-smalldoge-4">
                      {logoutMutation.isPending ? 'Logging out...' : 'Log Out'}
                    </span>
                  </div>
                </DropdownMenuItem>
              </>
            )}
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
