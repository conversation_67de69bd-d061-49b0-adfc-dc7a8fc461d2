import { Types } from 'mongoose';
import Stripe from 'stripe';

import { SignUpDto } from './dto/auth/sign-up.dto';

//Enums

export enum StripePlanMapping {
  FREE = 'free',
  PRO = 'pro',
  BUSINESS = 'business',
  ENTERPRISE = 'enterprise',
}

export type StripePlanStatus =
  | 'trialing'
  | 'active'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'paused';

export type OrganizationPlanStatus = StripePlanStatus | 'free';

export enum EmailTemplate {
  SIGNUP_CONFIRMATION = 'd-219112277f854bffa09d46c5f15d51ea',
  RESET_PASSWORD = 'd-8a32a009b63042a3b5808278f4dc4356',
  INVITATION_SENT = 'd-0277ca6163884534aef8b3c0b6bcaf76',
  CV_TEAM_DEL_SCHEDULED = 'd-a6e5f2369a144810acdb379547a32ce0',
  CV_ACCOUNT_DEL_SCHEDULED = 'd-bfd78a71a1b3467e8792ad8449816791',
  CV_ACCOUNT_DEL = 'd-2885724e730a4578b57d3df931667306',
  CV_SOLE_TEAM_DEL = 'd-0759d6f3214b4bd595d15e16b4494968',
  CV_TEAM_DEL = 'd-bee2c07587834c8b94c5e9a18f9e5d34',
}

export enum ErrorCode {
  EMAIL_TAKEN = 'Email already taken',
  LINK_INVALID = 'Link invalid',
  NAME_REQUIRED = 'Name is required',
  NOT_FOUND = 'Not found',
  PERMISSION_DENIED = 'Permission denied',
  UNEXPECTED_ERROR = 'unexpected_error',
  EMAIL_REQUIRED = 'email_required',
}

export enum SectionType {
  personalInfo = 'personalInfo',
  aboutMe = 'aboutMe',
  workHistory = 'workHistory',
  education = 'education',
  certifications = 'certifications',
  skills = 'skills',
  languages = 'languages',
  customSection = 'customSection',
}

export enum UserRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
}

export enum InviteStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
}

export enum TimeRange {
  hour = 'hour',
  day = 'day',
  month = 'month',
}

export const timeRangeDisplayMap = {
  [TimeRange.hour]: 'Hourly',
  [TimeRange.day]: 'Daily',
  [TimeRange.month]: 'Monthly',
};

export const timeRangeData = {
  [TimeRange.hour]: { name: 'Hour' },
  [TimeRange.day]: { name: 'Day' },
  [TimeRange.month]: { name: 'Month' },
};

export enum Currency {
  usd = 'usd',
  eur = 'eur',
  gbp = 'gbp',
}

export const currencyData = {
  [Currency.usd]: { name: 'USD', sign: '$' },
  [Currency.eur]: { name: 'EUR', sign: '€' },
  [Currency.gbp]: { name: 'GBP', sign: '£' },
};

export enum CvStatus {
  draft = 'draft',
  active = 'active',
}

export const cvStatusData = {
  [CvStatus.draft]: {
    name: 'Draft',
    bgColor: 'bg-msGray-6',
    triggerBgColor: 'bg-msYellow-1',
    triggerTextColor: 'text-msBlack',
    textColor: 'text-msGray-3',
  },
  [CvStatus.active]: {
    name: 'Active',
    bgColor: 'bg-msGreen-4',
    triggerBgColor: 'bg-msGreen-2',
    triggerTextColor: 'text-msWhite',
    textColor: 'text-msGreen-1',
  },
};

export enum Template {
  europass = 'europass',
  clean = 'clean',
  professional = 'professional',
  // federalUsa = 'federalUsa',
}

export const templateData = {
  [Template.europass]: 'Europass CV',
  [Template.clean]: 'Clean CV',
  [Template.professional]: 'Professional CV',
  // [Template.federalUsa]: 'Federal Resume (USA)',
};

export enum UserType {
  employee = 'employee',
  consultant = 'consultant',
  candidate = 'candidate',
}

export const userTypeData = {
  [UserType.employee]: { name: 'Employee', color: 'bg-msGreen-4' },
  [UserType.consultant]: { name: 'Consultant', color: 'bg-msYellow-2' },
  [UserType.candidate]: { name: 'Candidate', color: 'bg-msBlue-3' },
};

export enum MemberSource {
  muchskills = 'muchskills',
  linkedin = 'linkedin',
  cvinventory = 'cvinventory',
}

export const memberSourceData = {
  [MemberSource.muchskills]: {
    name: 'MuchSkills',
    icon: '/icons/muchskills.svg',
    color: 'bg-msBlack',
  },
  [MemberSource.linkedin]: {
    name: 'LinkedIn',
    icon: '/icons/linkedin.svg',
    color: 'bg-[#2867B2]',
  },
  [MemberSource.cvinventory]: {
    name: 'Add member',
    icon: 'clipboard-pen',
    color: 'bg-msMagicAi-2 text-msMagicAi-1',
  },
};

export enum GroupFilter {
  all = 'all',
  draft = 'draft',
}

export const groupFilterData = {
  [GroupFilter.all]: { name: 'All' },
  [GroupFilter.draft]: { name: 'Draft' },
};

export enum CvProfileFilter {
  all = 'all',
  hasCvProf = 'hasCvProf',
  hasNoCvProf = 'hasNoCvProf',
}

export const cvProfileFilterData = {
  [CvProfileFilter.all]: { name: 'All' },
  [CvProfileFilter.hasCvProf]: { name: 'Has profile' },
  [CvProfileFilter.hasNoCvProf]: { name: 'Has no profile' },
};

export enum AppPlanTier {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  BUSINESS = 'business',
  ENTERPRISE = 'enterprise', // Added enterprise
}

export enum ExpertiseLevelEnum {
  noLevel = 'noLevel',
  beginner = 'beginner',
  intermediate = 'intermediate',
  expert = 'expert',
}

export const expertiseLevelData = {
  [ExpertiseLevelEnum.noLevel]: { color: 'bg-msWhite', order: 4 },
  [ExpertiseLevelEnum.beginner]: { color: 'bg-msYellow-1', order: 3 },
  [ExpertiseLevelEnum.intermediate]: { color: 'bg-msGreen-2', order: 2 },
  [ExpertiseLevelEnum.expert]: { color: 'bg-msBlack', order: 1 },
};

export interface PlanFeature {
  name: string;
  value: string;
}
//Interfaces
export interface Plan {
  name: string;
  price: string;
  description: string;
  features: PlanFeature[];
  priceId?: string;
  tier: string;
}
export interface ResolvedInvite {
  itemName: string;
  organizationName: string;
  organizationPhoto: string;
  inviteItemCreated: boolean;
  errorCode?: ErrorCode;
}

export type SignUpData = SignUpDto & ResolvedInvite;

export interface CostRate {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

export interface Social {
  name: string;
  link: string;
}

export interface Member {
  _id: string;
  avatar?: string;
  firstName: string;
  lastName?: string;
  email?: string;
  location?: string;
  telephone?: string;
  currentPosition?: string;
  currentLevel?: string;
  yearsOfExperience?: number;
  languages: string[];
  type?: UserType;
  clients: string[];
  costRate?: CostRate;
  costToCompany?: CostRate;
  socials: string[];
  source: MemberSource;
  sourceId?: string;
  competencesAmount?: number;
  workExperience: WorkHistoryRecord[];
  education: EducationRecord[];
  certifications: MemberCertification[];
  skills: MemberSkill[];
  cvs: Cv[];
}

export interface EducationRecord {
  schoolName: string;
  degree?: string;
  description?: string;
  startDate?: string | Date;
  endDate?: string | Date;
}

export interface WorkHistoryRecord {
  companyName: string;
  roleTitle?: string;
  description?: string;
  startDate: string | Date;
  endDate?: string | Date;
  isCurrent?: boolean;
}

export interface MemberCertification {
  msId: string;
  name: string;
  organization: string;
}

export interface MemberSkill {
  msId: string;
  name: string;
  level: ExpertiseLevelEnum;
  isSoftware: boolean;
}

export interface EducationDataItem extends Partial<EducationRecord> {
  active?: boolean;
}

export interface WorkHistoryDataItem extends Partial<WorkHistoryRecord> {
  active?: boolean;
}

export interface CvPreferences {
  title: string;
  maxPages?: number;
  role?: string;
  level?: string;
  costRate?: CostRate;
  customer?: { _id: string; name: string };
  link?: string;
  contractStart?: Date;
  contractEnd?: Date;
  autoRenewal?: boolean;
  leastExperience?: number;
  maxExperience?: number;
  skills?: string[];
  description?: string;
}

export interface Cv {
  _id: string;
  template: Template;
  status: CvStatus;
  preferences: CvPreferences;
  sections: CvSections;
  createdAt: Date;
  updatedAt: Date;
  member?: Member;
}

export interface CvSections {
  personalInfo: {
    order: number;
    title: string;
    active: boolean;
    data: PersonalInfoData;
  };
  aboutMe: {
    order: number;
    title: string;
    active: boolean;
    data: AboutMeData;
  };
  workHistory: {
    order: number;
    title: string;
    active: boolean;
    data: WorkHistoryDataItem[];
  };
  education: {
    order: number;
    title: string;
    active: boolean;
    data: EducationDataItem[];
  };
  certifications: {
    order: number;
    title: string;
    active: boolean;
    data: CertificationsData;
  };
  skills: {
    order: number;
    title: string;
    active: boolean;
    data: SkillsData;
  };
  languages: {
    order: number;
    title: string;
    active: boolean;
    data: LanguagesData;
  };
  customSections: {
    order: number;
    title: string;
    active: boolean;
    data: CustomSectionData;
  }[];
}
export interface Paging {
  page: number;
  itemsPerPage: number;
}

export interface SectionBase {
  id: string;
  mandatory?: boolean;
  order: number;
  title: string;
  active: boolean;
}

//

export interface HidableInput {
  value: string;
  active: boolean;
}

export interface PersonalInfoData {
  firstName: HidableInput;
  lastName: HidableInput;
  jobTitle: HidableInput;
  location: HidableInput;
  nationality: HidableInput;
  email: HidableInput;
  telephone: HidableInput;
  socials: { inputs: string[]; active: boolean };
}

export interface AboutMeData {
  description: string;
}

export interface CertificationsData {
  description: string;
}

export interface SkillsData {
  description: string;
}

export interface LanguagesData {
  languages: string[];
}

export interface CustomSectionData {
  description: string;
}

export interface PersonalInfo extends SectionBase {
  data: PersonalInfoData;
}

export interface AboutMe extends SectionBase {
  data: AboutMeData;
}

export interface Certifications extends SectionBase {
  data: CertificationsData;
}

export interface Skills extends SectionBase {
  data: SkillsData;
}

export interface Languages extends SectionBase {
  data: LanguagesData;
}

export interface CustomSection extends SectionBase {
  data: CustomSectionData;
}

export interface Education extends SectionBase {
  data: EducationDataItem[];
}

export interface WorkHistory extends SectionBase {
  data: WorkHistoryDataItem[];
}

export type SectionData =
  | PersonalInfoData
  | AboutMeData
  | WorkHistoryDataItem[]
  | EducationDataItem[]
  | CertificationsData
  | SkillsData
  | LanguagesData
  | CustomSectionData;

export interface Section extends SectionBase {
  data: SectionData;
}

export interface PaginatedMembers {
  members: Member[];
  totalMembers: number;
}

export interface MuchskillsMember {
  email: string;
  name: string;
  image: string;
  title: string;
  location: string;
  competencesCount: number;
  cvsCount: number;
  profileExist: boolean;
  localId?: string | Types.ObjectId;
}

export interface PaginatedMuchskillsMembers {
  members: MuchskillsMember[];
  total: number;
}

export interface Organization {
  _id: string;
  name: string;
  photo?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  planStatus?: OrganizationPlanStatus;
  planId?: string;
  currentPeriodEnds?: Date;
  planTier?: StripePlanMapping;
  muchskillsIntegration?: MuchskillsIntegration;
  deletionScheduledDate?: Date;
  locations: string[];
  description: string;
  website: string;
  timezone: string;
}

export interface OrganizationFullInfo extends Organization {
  totalMembers: number;
  totalCvs: number;
}

export interface CustomStripeProductMetadata {
  id: string; // Plan identifier like 'free', 'pro', 'business', 'enterprise'
  app: 'cv'; // App identifier
  max_cv?: string; // String representation of the number
  profiles?: string; // String representation of the number
  users?: string; // String representation of the number
  // Add any other direct feature keys you expect from metadata
  [key: string]: string | undefined; // Allow other string keys for flexibility
}

export interface ApiStripeProduct
  extends Omit<Stripe.Product, 'default_price' | 'metadata'> {
  default_price: Stripe.Price | null;
  metadata: CustomStripeProductMetadata;
  prices: Stripe.Price[];
}

export interface MuchskillsIntegration {
  token: string;
  connected: boolean;
  lastSync?: Date;
}

export interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  avatar?: string | null;
  deletionScheduledDate?: Date;
}

export interface Customer {
  _id: string;
  name: string;
  createdAt: Date;
}

export interface Certification {
  msId: string;
  name: string;
  organization: string;
  image: string;
  code: string;
}

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export interface Message {
  role: MessageRole;
  content: string;
}

export interface Skill {
  msId: string;
  name: string;
  description?: string;
  image: string;
  isSoftware: boolean;
}

export interface SkillFromMuchskills {
  _id: string;
  name: string;
  description?: string;
  image: string;
  type: string;
}

export interface CertificateFromMuchskills {
  _id: string;
  name: string;
  certificationOrganization: { name: string; image: string };
  code: string;
}

export interface Invite {
  _id: string;
  email: string;
  role: UserRole;
  status: InviteStatus;
  createdAt: string;
  updatedAt: string;
}
