import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { InviteModule } from '../invite/invite.module';
import { OrganizationModule } from '../organization/organization.module';
import {
  Organization,
  OrganizationSchema,
} from '../organization/organization.schema';
import { TokenModule } from '../token/token.module';
import { UsersModule } from '../users/users.module';
import { AuthGuard } from './guards/auth.guard';
import { MailService } from '../mail/mail.service';

@Global()
@Module({
  imports: [
    UsersModule,
    InviteModule,
    TokenModule,
    OrganizationModule,
    HttpModule,
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET_KEY,
      signOptions: { expiresIn: '30d' },
    }),
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
  ],
  providers: [AuthService, AuthGuard, MailService],
  controllers: [AuthController],
  exports: [AuthService, AuthGuard, UsersModule],
})
export class AuthModule {}
