import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosError } from 'axios';
import { compare, hash } from 'bcrypt';
import { Model, Types } from 'mongoose';
import { catchError, firstValueFrom } from 'rxjs';
import { DEFAULT_USER_NAME } from 'shared/constants';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { UpdatePasswordDto } from 'shared/dto/auth/update-password.dto';
import { ErrorCode } from 'shared/types';
import { UserRole } from 'shared/types';

import { AuthUserDto } from '../global/dto/auth-user.dto';
import { InviteService } from '../invite/invite.service';
import { MailService } from '../mail/mail.service';
import {
  Organization,
  OrganizationDocument,
} from '../organization/organization.schema';
import { OrganizationService } from '../organization/organization.service';
import { TokenService } from '../token/token.service';
import { OrganizationMembership, User, UserStatus } from '../users/user.schema';
import { UsersService } from '../users/users.service';

// Response types for getCurrentUser using existing schema types
type UserResponse = Pick<
  User,
  | '_id'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'avatar'
  | 'deletionScheduledDate'
> & {
  role: UserRole;
  availableOrganizations: EnrichedOrganization[];
};

type OrganizationResponse = Pick<
  Organization,
  '_id' | 'name' | 'photo' | 'planTier' | 'planStatus' | 'muchskillsIntegration'
>;

type EnrichedOrganization = OrganizationMembership & {
  name: string;
  photo?: string;
};

interface GetCurrentUserResponse {
  user: UserResponse;
  organization?: OrganizationResponse;
  needsOrganizationCreation?: boolean;
  organizationSwitched?: boolean;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(Organization.name)
    private organizationModel: Model<OrganizationDocument>,
    private readonly usersService: UsersService,
    private readonly tokenService: TokenService,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService,
    private readonly organizationService: OrganizationService,
    private readonly httpService: HttpService,
    private readonly inviteService: InviteService,
  ) {}

  public async signUp(signUpDto: SignUpDto, token?: string) {
    signUpDto.email = signUpDto.email.trim().toLowerCase();

    const user = await this.usersService.findOne({ email: signUpDto.email });
    if (user?.status === UserStatus.active) {
      throw new BadRequestException(ErrorCode.EMAIL_TAKEN);
    }

    if (token) {
      // Validate invite before proceeding and get invite data
      const invite = await this.inviteService.validateInviteDuringSignup(
        token,
        signUpDto.email,
      );

      // Create user without organization (invite will add them to org)
      const tokenDoc = await this.tokenService.generateToken(signUpDto);
      const { user } = await this.activateUser(
        tokenDoc.token,
        true,
        invite.name,
      );

      // Accept invite which will add user to organization with correct role
      const acceptedInvite = await this.inviteService.acceptInvite(token, {
        email: signUpDto.email,
        _id: user._id,
      });

      // Get organization details
      const organizationId = acceptedInvite.organization.toString();
      const organization =
        await this.organizationService.findById(organizationId);

      // Generate JWT token with organization info
      const payload = {
        sub: user._id.toString(),
        email: user.email,
        role: acceptedInvite.role,
        organizationId: organizationId,
      };
      const accessToken = await this.jwtService.signAsync(payload);

      return {
        email: user.email,
        token: tokenDoc.token,
        accessToken,
        organization: organization
          ? {
              id: organization._id.toString(),
              name: organization.name,
              photo: organization.photo || null,
            }
          : null,
      };
    } else {
      const tokenDoc = await this.tokenService.generateToken(signUpDto);
      await this.mailService.sendSignUpConfirmation(signUpDto, tokenDoc.token);
      return { email: signUpDto.email };
    }
  }

  public async activateUser(
    token: string,
    isInviteActivation = false,
    inviteName?: string,
  ) {
    const tokenDoc = await this.tokenService.findToken(token);

    if (!tokenDoc) {
      throw new Error('Token not found');
    }

    let organization;
    const userRole = UserRole.OWNER;

    if (isInviteActivation) {
      // For invite activation, don't create organization - user will be added to existing org
      organization = null;
    } else {
      // For regular signup, create organization
      organization = await this.organizationService.createOrganization({
        name: tokenDoc.email,
      });
    }

    const user = await this.usersService.create({
      firstName: inviteName || DEFAULT_USER_NAME,
      email: tokenDoc.email,
      password: tokenDoc.password,
      status: UserStatus.active,
      availableOrganizations: organization
        ? [
            {
              orgId: organization._id,
              role: UserRole.OWNER,
              joinedDate: new Date(),
            },
          ]
        : [],
      organization: organization?._id,
    });

    await this.tokenService.deleteToken(token);

    // Generate JWT token for auto-login
    const payload = {
      sub: user._id.toString(),
      email: user.email,
      role: userRole,
      organizationId: organization?._id?.toString(),
    };
    const accessToken = await this.jwtService.signAsync(payload);

    const { password, ...userWithoutPassword } = user;

    const organizationData = organization
      ? {
          id: organization._id.toString(),
          name: organization.name,
          photo: organization.photo || null,
        }
      : null;

    return {
      accessToken,
      user: userWithoutPassword,
      organization: organizationData,
    };
  }

  public async signIn(dto: SignInDto, token?: string) {
    dto.email = dto.email.trim().toLowerCase();
    const { email, password } = dto;

    const result = await this.authenticateAndGenerateToken(email, password);
    if (token) {
      await this.inviteService.acceptInvite(token, result.user);
    }
    return result;
  }

  private async authenticateAndGenerateToken(
    email: string,
    passwordToCheck: string,
  ) {
    const user = await this.usersService.findOneAndPopulateOrganization(
      { email },
      '+password +deletionScheduledDate',
    );

    if (!user?.password) {
      throw new UnauthorizedException('Invalid credentials.');
    }

    const isMatch = await compare(passwordToCheck, user.password);
    if (!isMatch) {
      throw new UnauthorizedException('Invalid credentials.');
    }

    // Normal login flow
    const organization = await this.organizationService.findById(
      user.organization,
    );

    // Check if the active organization is scheduled for deletion
    if (organization && organization.deletionScheduledDate) {
      const newActiveOrg = await this.switchOrganizationIfScheduledForDeletion(
        user._id.toString(),
      );

      if (newActiveOrg) {
        // Update organization to the new active one
        const payload = {
          sub: user._id.toString(),
          email: user.email,
        };
        const accessToken = await this.jwtService.signAsync(payload);

        const { deletionScheduledDate, password, ...userWithoutPassword } =
          user;

        const organizationData = {
          id: newActiveOrg._id.toString(),
          name: newActiveOrg.name,
          photo: newActiveOrg.photo || null,
        };

        return {
          accessToken,
          user: userWithoutPassword,
          organization: organizationData,
          organizationSwitched: true,
        };
      } else {
        // No valid organizations, don't auto-create, return flag indicating user needs to create one
        const payload = {
          sub: user._id.toString(),
          email: user.email,
        };
        const accessToken = await this.jwtService.signAsync(payload);

        const { deletionScheduledDate, password, ...userWithoutPassword } =
          user;

        return {
          accessToken,
          user: userWithoutPassword,
          needsOrganizationCreation: user.availableOrganizations.length === 0,
        };
      }
    } else if (
      user.deletionScheduledDate &&
      !organization?.deletionScheduledDate
    ) {
      // throw new UnauthorizedException(
      //   'Your account is scheduled for deletion.',
      // );
    }

    const payload = {
      sub: user._id.toString(),
      email: user.email,
    };
    const accessToken = await this.jwtService.signAsync(payload);

    const { deletionScheduledDate, password, ...userWithoutPassword } = user;

    const organizationData = {
      id: organization?._id.toString(),
      name: organization?.name,
      photo: organization?.photo || null,
    };

    return {
      accessToken,
      user: userWithoutPassword,
      organization: organizationData,
    };
  }

  // This method should using oauth2.0 connect our application to muchskills application. for now it will just store the hardcoded auth token in the user record
  public async connectWithMuchSkills(
    user: AuthUserDto,
    orgId: string,
    authToken: string,
  ) {
    if (!user) {
      throw new UnauthorizedException();
    }

    await firstValueFrom(
      this.httpService
        .get(
          `${process.env.MUCHSKILLS_API_URL || 'https://test.muchskills.com'}/api/v1/auth/check-token`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
            },
          },
        )
        .pipe(
          catchError((error: AxiosError) => {
            switch (error.response?.status) {
              case 401:
                throw new UnauthorizedException({
                  message: 'Token is not valid!',
                });
              case 429:
                throw new BadRequestException('Too many requests!');
              case 503:
                throw new BadRequestException(
                  'Service unavailable: Maintenance in progress.',
                );
              default:
                throw new BadRequestException('Something went wrong.');
            }
          }),
        ),
    );

    await this.organizationService.updateMuchskillsAuthToken(orgId, authToken);
  }

  public async disconnectMuchSkills(user: AuthUserDto, orgId: string) {
    if (!user) {
      throw new UnauthorizedException();
    }

    await this.organizationModel.findOneAndUpdate(
      { _id: orgId },
      {
        $unset: { muchskillsMembers: '', muchskillsIntegration: '' },
      },
    );
  }

  public async requestPasswordReset(email: string) {
    email = email.trim().toLowerCase();

    // Check if user exists
    const user = await this.usersService.findOne({ email });
    if (!user) {
      // For security reasons, don't reveal that the user doesn't exist
      return {
        message:
          'If your email is registered, you will receive reset instructions',
      };
    }

    // Generate a token for password reset
    const tokenDoc = await this.tokenService.generateToken({
      email,
      password: '',
    });

    // Send the password reset email
    await this.mailService.sendPasswordResetEmail(
      email,
      user.firstName,
      tokenDoc.token,
    );

    return {
      message: 'Password reset instructions have been sent to your email',
    };
  }

  public async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, newPassword } = resetPasswordDto;

    // Verify the token
    const tokenDoc = await this.tokenService.findToken(token);
    if (!tokenDoc) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Find the user by email
    const user = await this.usersService.findOne({ email: tokenDoc.email });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Hash the new password
    const hashedPassword = await hash(newPassword, 10);

    // Update the user's password
    await this.usersService.updatePassword(user._id, hashedPassword);

    // Delete the used token
    await this.tokenService.deleteToken(token);

    return { message: 'Password has been reset successfully' };
  }

  public async getCurrentUser(
    authUser: AuthUserDto,
  ): Promise<GetCurrentUserResponse> {
    const user = await this.usersService.findOneAndPopulateOrganization(
      { _id: authUser._id },
      '+availableOrganizations +deletionScheduledDate',
    );

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const organization = await this.organizationService.findById(
      user.organization,
    );

    // Get enriched organizations for the user
    const enrichedOrganizations = await this.getValidOrganizations(user);

    // Handle case where organization is not found
    if (!organization) {
      return {
        user: this.buildUserResponse(user, UserRole.MEMBER, []),
        needsOrganizationCreation: user.availableOrganizations.length === 0,
      };
    }

    // Normal flow - organization is valid
    const role = this.resolveUserRole(user, user.organization);
    return {
      user: this.buildUserResponse(user, role, enrichedOrganizations),
      organization: this.buildOrganizationResponse(organization),
    };
  }

  // Helper methods for getCurrentUser
  private buildUserResponse(
    user: User,
    role: UserRole,
    availableOrganizations: EnrichedOrganization[],
  ): UserResponse {
    return {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      avatar: user.avatar,
      role,
      availableOrganizations,
      deletionScheduledDate: user.deletionScheduledDate,
    };
  }

  private buildOrganizationResponse(
    organization: OrganizationDocument,
  ): OrganizationResponse {
    return {
      _id: organization._id,
      name: organization.name,
      photo: organization.photo,
      planTier: organization.planTier,
      planStatus: organization.planStatus,
      muchskillsIntegration: organization.muchskillsIntegration,
    };
  }

  private resolveUserRole(
    user: User,
    organizationId: Types.ObjectId,
  ): UserRole {
    const currentOrgMembership = user.availableOrganizations?.find(
      (org: OrganizationMembership) =>
        org.orgId.toString() === organizationId.toString(),
    );
    return currentOrgMembership?.role || UserRole.MEMBER;
  }

  private async getValidOrganizations(
    user: User,
  ): Promise<EnrichedOrganization[]> {
    const allOrganizations = await this.organizationModel
      .find({
        _id: { $in: user.availableOrganizations.map((org) => org.orgId) },
      })
      .lean()
      .exec();

    const orgMap = new Map();
    allOrganizations.forEach((org) => {
      orgMap.set(org._id.toString(), {
        name: org.name,
        photo: org.photo || null,
      });
    });

    return user.availableOrganizations.map((org) => {
      const orgDetails = orgMap.get(org.orgId.toString());
      return {
        ...org,
        name: orgDetails?.name || 'Unknown Organization',
        photo: orgDetails?.photo || null,
      };
    });
  }

  private async switchOrganizationIfScheduledForDeletion(
    userId: string,
  ): Promise<OrganizationDocument | null> {
    const availableOrgs =
      await this.organizationService.getUserOrganizations(userId);
    const validOrgs = availableOrgs.filter((org) => !org.deletionScheduledDate);

    if (validOrgs.length > 0) {
      // Set the first valid organization as active
      await this.usersService.setActiveOrganization(userId, validOrgs[0]._id);

      // Return the new active organization
      return this.organizationService.findById(validOrgs[0]._id);
    }

    return null;
  }

  async updatePassword(user: AuthUserDto, dto: UpdatePasswordDto) {
    const { oldPassword, newPassword } = dto;

    const currentUser = await this.usersService.findUserWithPassword({
      _id: user._id,
    });
    if (!currentUser) {
      throw new UnauthorizedException('User not found');
    }

    const isOldPasswordValid = await compare(oldPassword, currentUser.password);
    if (!isOldPasswordValid) {
      throw new BadRequestException('Old password is incorrect');
    }

    const salt = 10;
    const hashedNewPassword = await hash(newPassword, salt);

    await this.usersService.updatePassword(user._id, hashedNewPassword);

    return { message: 'Password updated successfully' };
  }
}
