import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { compare } from 'bcrypt';
import { Model, Types } from 'mongoose';
import { UserRole, OrganizationFullInfo } from 'shared/types';

import { Organization, OrganizationDocument } from './organization.schema';
import { CvDocument } from '../cvs/cv.schema';
import { CvsService } from '../cvs/cvs.service';
import { S3Provider } from '../global/s3.provider';
import { InviteService } from '../invite/invite.service';
import { MailService } from '../mail/mail.service';
import { MemberDocument } from '../members/member.schema';
import { StripeService } from '../stripe/stripe.service';
import { User } from '../users/user.schema';
import { UsersService } from '../users/users.service';

const DELETION_DELAY_DAYS = 7;

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectModel(Organization.name)
    private organizationModel: Model<OrganizationDocument>,
    @InjectModel('Member') private memberModel: Model<MemberDocument>,
    @InjectModel('Cv') private cvModel: Model<CvDocument>,
    @Inject(forwardRef(() => InviteService))
    private readonly inviteService: InviteService,
    @Inject(forwardRef(() => UsersService))
    public readonly usersService: UsersService,
    @Inject(forwardRef(() => CvsService))
    private readonly cvService: CvsService,
    private readonly stripeService: StripeService,
    private readonly mailService: MailService,
  ) {}

  async createOrganization({ name }: { name: string }): Promise<Organization> {
    const organization = await new this.organizationModel({
      name,
    }).save();

    return organization;
  }

  async findById(
    id: string | Types.ObjectId,
  ): Promise<OrganizationDocument | null> {
    const organization = await this.organizationModel
      .findById(id)
      .select('+muchskillsIntegration.token')
      .exec();

    // Mask the token if it exists
    if (organization?.muchskillsIntegration?.token) {
      const token = organization.muchskillsIntegration.token;
      organization.muchskillsIntegration.token = `**************************${token.slice(-4)}`;
    }

    return organization;
  }

  async updateOrganization(
    id: string | Types.ObjectId,
    updateData: Partial<Organization> & { $unset?: Record<string, number> },
  ): Promise<OrganizationDocument | null> {
    // Extract $unset operator if present
    const { $unset, ...setData } = updateData;

    // Create update object
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateObj: any = {};
    if (Object.keys(setData).length > 0) {
      updateObj.$set = setData;
    }

    // Add $unset operator if present
    if ($unset && Object.keys($unset).length > 0) {
      updateObj.$unset = $unset;
    }

    const organization = await this.organizationModel
      .findByIdAndUpdate(id, updateObj, { new: true })
      .exec();

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found.`);
    }
    return organization;
  }

  async getOrganizationUsers(
    organizationId: string | Types.ObjectId,
    searchTerm?: string,
    includeScheduledForDeletion = false,
  ): Promise<User[]> {
    return this.usersService.getOrganizationUsers(
      organizationId,
      searchTerm,
      includeScheduledForDeletion,
    );
  }

  async deleteOrganization(
    organizationId: string | Types.ObjectId,
    userId: string | Types.ObjectId,
    password: string,
  ) {
    const organization = await this.organizationModel.findById(organizationId);

    if (!organization) {
      this.logger.warn(
        `Organization ${organizationId} not found for deletion.`,
      );
      throw new NotFoundException('Organization not found');
    }

    // Verify user password
    const user = await this.usersService.findUserWithPassword({
      _id: new Types.ObjectId(userId),
    });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const isPasswordValid = await compare(password, user.password);
    if (!isPasswordValid) {
      throw new BadRequestException('Invalid password');
    }

    const deletionDate = new Date();
    deletionDate.setDate(deletionDate.getDate() + DELETION_DELAY_DAYS);

    await this.organizationModel.findByIdAndUpdate(organizationId, {
      deletionScheduledDate: deletionDate,
    });

    this.logger.log(
      `Organization ${organizationId} scheduled for deletion on ${deletionDate.toISOString()}.`,
    );

    const users = await this.usersService.getOrganizationUsers(organizationId);

    for (const user of users) {
      const userOrgs = await this.usersService.getUserOrganizations(user._id);

      const otherOrgs = userOrgs.filter(
        (org) => org._id.toString() !== organizationId.toString(),
      );

      if (otherOrgs.length > 0) {
        // User has other organizations, set the first one as active
        await this.usersService.setActiveOrganization(
          user._id,
          otherOrgs[0]._id,
        );
        this.logger.log(
          `User ${user._id} moved to organization ${otherOrgs[0]._id} as their active organization.`,
        );
      } else {
        // User has no other organizations, mark for deletion
        await this.usersService.markUserForDeletion(user._id, deletionDate);
        this.logger.log(
          `User ${user._id} marked for deletion on ${deletionDate.toISOString()}.`,
        );
      }

      // Send notification email to user about the scheduled deletion
      await this.mailService.sendOrganizationDeletionNotification(
        user.email,
        user.firstName,
        user.lastName,
        organization.name,
        deletionDate,
      );
    }

    // If the organization has a Stripe subscription, cancel it
    if (organization.stripeCustomerId) {
      await this._cancelStripeSubscriptions(
        organization.stripeCustomerId,
        organizationId,
      );
    } else {
      this.logger.log(
        `Organization ${organizationId} has no Stripe customer ID. Skipping Stripe cancellation.`,
      );
    }

    return {
      message: `Organization scheduled for deletion on ${deletionDate.toISOString()}`,
      deletionDate,
    };
  }

  async cancelOrganizationDeletion(
    organizationId: string | Types.ObjectId,
  ): Promise<Organization | null> {
    const organization = await this.organizationModel
      .findById(organizationId)
      .select('+deletionScheduledDate');

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (!organization.deletionScheduledDate) {
      throw new BadRequestException(
        'No deletion is scheduled for this organization',
      );
    }

    this.logger.log(`Canceling deletion for organization ${organizationId}.`);

    // Remove the deletion scheduled date from organization
    const updatedOrganization = await this.organizationModel.findByIdAndUpdate(
      organizationId,
      { $unset: { deletionScheduledDate: '' } },
      { new: true },
    );

    // Get all users in the organization
    const users = await this.usersService.getOrganizationUsers(organizationId);

    for (const user of users) {
      const userOrgs = await this.usersService.getUserOrganizations(user._id);

      const otherOrgs = userOrgs.filter(
        (org) => org._id.toString() !== organizationId.toString(),
      );

      // If user has no other organizations and was marked for deletion, cancel their deletion
      if (otherOrgs.length === 0) {
        await this.usersService.markUserForDeletion(user._id, null);
        this.logger.log(
          `User ${user._id} deletion canceled due to organization ${organizationId} deletion being canceled.`,
        );
      }
    }

    this.logger.log(`Deletion canceled for organization ${organizationId}.`);

    return updatedOrganization;
  }

  /**
   * This method will be called by a cron job to execute the actual deletion
   * of organizations that have reached their scheduled deletion date
   */
  async executeScheduledDeletions() {
    const today = new Date();

    const orgsToDelete = await this.organizationModel.find({
      deletionScheduledDate: { $lte: today },
    });

    this.logger.log(
      `Found ${orgsToDelete.length} organizations scheduled for deletion.`,
    );

    // Process each organization
    for (const org of orgsToDelete) {
      try {
        // Get all users of this organization before deleting it
        const orgUsers = await this.getOrganizationUsers(
          org._id,
          undefined,
          true,
        );
        this.logger.log(
          `Found ${orgUsers.length} users in organization ${org._id} (${org.name}) scheduled for deletion.`,
        );

        // Send appropriate emails to each user
        for (const orgUser of orgUsers) {
          try {
            // Get user's full data to access firstName, lastName, email
            const user = await this.usersService.findById(
              orgUser._id.toString(),
            );
            if (!user) {
              this.logger.warn(
                `User ${orgUser._id} not found, skipping email notification.`,
              );
              continue;
            }

            // Get user's total organizations to determine email scenario
            const userOrgs = await this.usersService.getUserOrganizations(
              user._id,
              true,
            );

            if (userOrgs.length === 1) {
              // Scenario A: User has only this organization - they will be deleted
              await this.mailService.sendSoleOrganizationDeletedConfirmation(
                user.email,
                user.firstName,
                user.lastName,
                org.name,
              );
              this.logger.log(
                `Sent sole organization deletion email to ${user.email} for organization ${org.name}`,
              );
            } else {
              // Scenario B: User has multiple organizations - they keep their account
              await this.mailService.sendOrganizationDeletedConfirmation(
                user.email,
                user.firstName,
                user.lastName,
                org.name,
              );
              this.logger.log(
                `Sent organization deletion email to ${user.email} for organization ${org.name}`,
              );
            }
          } catch (error) {
            if (error instanceof Error) {
              this.logger.error(
                `Error sending deletion email to user ${orgUser._id} for organization ${org._id}: ${error.message}`,
              );
            } else {
              this.logger.error(
                `Error sending deletion email to user ${orgUser._id} for organization ${org._id}: ${String(error)}`,
              );
            }
          }
        }

        // Delete organization and related data from our DB
        await this.organizationModel.findByIdAndDelete(org._id);
        await this.inviteService.deleteOrganizationInvites(org._id);
        await this.usersService.deleteOrganizationUsers(org._id);
        await this.memberModel.deleteMany({ organization: org._id }).exec();
        await this.cvService.deleteOrganizationCvs(org._id);

        this.logger.log(
          `Deleted organization ${org._id} and all associated data (invites, users, members, cvs).`,
        );
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `Error deleting organization ${org._id}: ${error.message}`,
            error.stack,
          );
        } else {
          this.logger.error(
            `Error deleting organization ${org._id}: ${String(error)}`,
          );
        }
      }
    }

    await this.usersService.executeScheduledUserDeletions();

    return {
      deletedCount: orgsToDelete.length,
    };
  }

  async updateMuchskillsAuthToken(
    orgId: string | Types.ObjectId,
    authToken: string,
  ) {
    return this.organizationModel
      .findByIdAndUpdate(
        orgId,
        {
          $set: {
            'muchskillsIntegration.token': authToken,
            'muchskillsIntegration.connected': true,
          },
        },
        { new: true },
      ) // Added {new: true}
      .exec();
  }

  async getMuchskillsAuthToken(
    orgId: string | Types.ObjectId,
  ): Promise<string | null> {
    const organization = await this.organizationModel
      .findById(orgId)
      .select('+muchskillsIntegration.token')
      .exec();

    return organization?.muchskillsIntegration?.token || null;
  }

  // TEST('vladyslav'): test upload logo
  // TODO('vladyslav'): add upload logo, fix multer
  // async uploadLogo(user: AuthUserDto, img?: Express.Multer.File) {
  //   let url: string;
  //   if (img) {
  //     const sendData = await this.s3Provider.save(img.buffer, img.originalname);
  //     url = sendData.Location;
  //   }
  //   await this.organizationModel.findOneAndUpdate(
  //     { _id: user.organization._id },
  //     { $set: { photo: url } }
  //   );
  //   return { url };
  // }

  async setActiveOrganization(
    userId: string | Types.ObjectId,
    organizationId: string | Types.ObjectId,
  ) {
    // Check if organization is scheduled for deletion
    const organization = await this.findById(organizationId);

    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${organizationId} not found`,
      );
    }

    await this.usersService.setActiveOrganization(userId, organizationId);
    return organization;
  }

  async getUserOrganizations(userId: string | Types.ObjectId) {
    const userOrgs = await this.usersService.getUserOrganizations(userId);

    // Fetch additional organization data including deletionScheduledDate
    const orgsWithDeletionData = await Promise.all(
      userOrgs.map(async (org) => {
        const fullOrgData = await this.findById(org._id);
        return {
          ...org,
          deletionScheduledDate: fullOrgData?.deletionScheduledDate,
        };
      }),
    );

    return orgsWithDeletionData;
  }

  async addUserToOrganization(
    organizationId: string | Types.ObjectId,
    userId: string | Types.ObjectId,
    role: UserRole,
  ) {
    const result = await this.usersService.addUserToOrganization(
      organizationId,
      userId,
      role,
    );

    return result;
  }

  // New methods for Stripe integration
  async findByStripeCustomerId(
    stripeCustomerId: string,
  ): Promise<OrganizationDocument | null> {
    return this.organizationModel.findOne({ stripeCustomerId }).exec();
  }

  async updateByStripeCustomerId(
    stripeCustomerId: string,
    updateData: Partial<Organization> & { $unset?: Record<string, number> },
  ): Promise<OrganizationDocument | null> {
    // Extract $unset operator if present
    const { $unset, ...setData } = updateData;

    // Create update object
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateObj: any = {};
    if (Object.keys(setData).length > 0) {
      updateObj.$set = setData;
    }

    // Add $unset operator if present
    if ($unset && Object.keys($unset).length > 0) {
      updateObj.$unset = $unset;
    }

    const organization = await this.organizationModel
      .findOneAndUpdate(
        { stripeCustomerId },
        updateObj,
        { new: true }, // Return the updated document
      )
      .exec();

    if (!organization) {
      this.logger.warn(
        `Organization not found with Stripe Customer ID: ${stripeCustomerId} for update.`,
      );
      // Do not throw NotFoundException here as Stripe webhooks might try to update
      // a customer that was created but the initial DB save failed or was delayed.
      // The webhook handler should log this and Stripe will retry.
    } else {
      this.logger.log(
        `Organization ${organization._id} updated via Stripe Customer ID ${stripeCustomerId}`,
      );
    }
    return organization;
  }

  async getOrganizationFullInfo(
    organizationId: string | Types.ObjectId,
  ): Promise<OrganizationFullInfo> {
    const organization = await this.organizationModel
      .findById(organizationId)
      .lean()
      .exec();
    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${organizationId} not found.`,
      );
    }

    const memberCount = await this.memberModel.countDocuments({
      organization: organizationId,
    });
    const cvCount = await this.cvModel.countDocuments({
      organization: organizationId,
    });

    return {
      ...organization,
      _id: organization._id.toString(),
      totalMembers: memberCount,
      totalCvs: cvCount,
    };
  }

  /**
   * Find organizations with active subscription statuses for Stripe sync
   * Excludes organizations with 'free' or 'canceled' status
   */
  async findOrganizationsWithActiveSubscriptions(): Promise<
    OrganizationDocument[]
  > {
    return this.organizationModel
      .find({
        stripeCustomerId: { $exists: true, $ne: null },
        planStatus: {
          $nin: ['free', 'canceled'],
        },
      })
      .exec();
  }

  private async _cancelStripeSubscriptions(
    stripeCustomerId: string,
    organizationId: string | Types.ObjectId,
  ) {
    this.logger.log(
      `Attempting to cancel Stripe subscriptions for customer ${stripeCustomerId} (organization ${organizationId})`,
    );
    try {
      const subscriptions = await this.stripeService
        .getStripeInstance()
        .subscriptions.list({
          customer: stripeCustomerId,
          status: 'active', // Only cancel active ones
        });

      if (subscriptions.data.length === 0) {
        this.logger.log(
          `No active Stripe subscriptions found for customer ${stripeCustomerId}.`,
        );
        return;
      }

      for (const subscription of subscriptions.data) {
        this.logger.log(
          `Cancelling Stripe subscription ${subscription.id} for customer ${stripeCustomerId}`,
        );
        await this.stripeService
          .getStripeInstance()
          .subscriptions.cancel(subscription.id, {
            cancellation_details: {
              comment: 'Organization scheduled for deletion',
            },
          });
        this.logger.log(
          `Successfully cancelled Stripe subscription ${subscription.id}`,
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error cancelling Stripe subscriptions for customer ${stripeCustomerId} (organization ${organizationId}): ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error(
          `Error cancelling Stripe subscriptions for customer ${stripeCustomerId} (organization ${organizationId}): ${String(error)}`,
        );
      }

      throw new BadRequestException(
        `Failed to cancel Stripe subscriptions for organization ${organizationId}. Please try again or contact support.`,
      );
    }
  }

  async uploadOrganizationPhoto(
    organizationId: string,
    photo?: Express.Multer.File,
  ) {
    if (!photo) {
      throw new Error('File is not passed');
    }

    const sendData = await new S3Provider().save(
      photo.buffer,
      photo.mimetype,
      `organization/${organizationId}/photo/${photo.originalname}`,
    );

    await this.organizationModel.findByIdAndUpdate(organizationId, {
      photo: sendData.Location,
    });

    return { photo: sendData.Location };
  }

  async removeOrganizationPhoto(organizationId: string) {
    const organization = await this.organizationModel.findById(organizationId);

    if (!organization) {
      throw new NotFoundException(
        `Organization with ID "${organizationId}" not found`,
      );
    }

    const organizationPhoto = organization.photo;

    if (organizationPhoto) {
      const photoKey = organizationPhoto.replace(
        new RegExp('^' + process.env.AWS_BUCKET_URL + '/'),
        '',
      );
      await new S3Provider().delete(photoKey);

      await this.organizationModel.findByIdAndUpdate(organizationId, {
        $unset: { photo: '' },
      });
    }

    return { photo: null };
  }
}
