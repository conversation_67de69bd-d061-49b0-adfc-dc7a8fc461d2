import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  MemberSource,
  StripePlanMapping,
  OrganizationPlanStatus,
} from 'shared/types';

import {
  MemberCertification,
  MemberEducation,
  MemberExperience,
  MemberSkill,
} from '../members/member.schema';

@Schema({ timestamps: true })
export class MuchskillsIntegration {
  @Prop({ type: String, select: false })
  token: string;

  @Prop({ type: Boolean })
  connected: boolean;

  @Prop({ type: Date })
  lastSync?: Date;
}

@Schema({ timestamps: true })
export class MuchskillsMember {
  @Prop({ type: String })
  email: string;

  @Prop({ type: String })
  name: string;

  @Prop({ type: String })
  avatar: string;

  @Prop({ type: String })
  currentPosition: string;

  @Prop({ type: String })
  about: string;

  @Prop({ type: String })
  department: string;

  @Prop({ type: String })
  location: string;

  @Prop({ type: [String] })
  socials: [string];

  @Prop({ type: [String] })
  references: [string];

  @Prop({ type: [String] })
  tags: [string];

  @Prop({ type: [MemberExperience] })
  workExperience: MemberExperience[];

  @Prop({ type: [MemberEducation] })
  education: MemberEducation[];

  @Prop({ type: [MemberSkill] })
  skills: MemberSkill[];

  @Prop({ type: [MemberCertification] })
  certifications: MemberCertification[];

  @Prop({ type: String, enum: MemberSource, required: true })
  source: MemberSource;
}

@Schema({ timestamps: true })
export class Organization {
  _id: Types.ObjectId;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'Invite' }] })
  invites: Types.ObjectId[];

  @Prop({
    type: String,
  })
  photo?: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'CV' }] })
  cvs: Types.ObjectId[];

  @Prop({ type: [MuchskillsMember] })
  muchskillsMembers?: MuchskillsMember[];

  @Prop({ type: [String] })
  locations: string[];

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  website: string;

  @Prop({ type: String })
  timezone: string;

  //Integrations
  @Prop({ type: MuchskillsIntegration })
  muchskillsIntegration?: MuchskillsIntegration;

  // Stripe related fields
  @Prop({ type: String, unique: true, sparse: true })
  stripeCustomerId?: string;

  @Prop({ type: String, unique: true, sparse: true })
  stripeSubscriptionId?: string;

  @Prop({ type: String, default: 'free' })
  planStatus?: OrganizationPlanStatus;

  @Prop({ type: Date })
  currentPeriodEnds?: Date;

  @Prop({ type: String })
  planId?: string;

  @Prop({
    type: String,
    enum: StripePlanMapping,
    default: StripePlanMapping.FREE,
  })
  planTier?: StripePlanMapping;

  @Prop({ type: Date })
  deletionScheduledDate?: Date;
}

export type OrganizationDocument = Organization & Document;
export const OrganizationSchema = SchemaFactory.createForClass(Organization);
