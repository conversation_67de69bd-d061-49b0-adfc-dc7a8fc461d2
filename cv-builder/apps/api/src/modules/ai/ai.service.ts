import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable } from '@nestjs/common';
import { AxiosError } from 'axios';
import { catchError, firstValueFrom } from 'rxjs';
import { Message } from 'shared/types';

import { CvDocument, CvSections } from '../cvs/cv.schema';
import { MemberDocument } from '../members/member.schema';

@Injectable()
export class AiService {
  constructor(private readonly httpService: HttpService) {}

  async regenerateCvSections(
    cv: CvDocument,
    member: MemberDocument,
    customerName: string | null,
    query: string,
    messages?: Message[],
  ): Promise<Partial<CvSections>> {
    try {
      const result = await firstValueFrom(
        this.httpService
          .post(process.env.AI_API_URL + '/ai/regenerate-cv', {
            cv: cv.toObject(),
            member: member.toObject(),
            customerName: customerName,
            query,
            messages: messages || [],
            token: process.env.AI_API_TOKEN,
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.log('regenerateCvSections error: ', error);
              throw new BadRequestException('AI service error');
            }),
          ),
      );

      const response = result?.data;

      // Check if there was an error from the AI service
      if (response?.error) {
        throw new BadRequestException(
          response.changesSummary || 'AI service error',
        );
      }

      return response;
    } catch (error) {
      console.log('regenerateCvSections error: ', error);
      throw new BadRequestException('AI service error');
    }
  }
}
